import { _decorator, <PERSON><PERSON>, Component, director, Node } from 'cc';
import { userInfo } from './userInfo';
import { DataManager } from '../Managers/DataManager';
const { ccclass, property } = _decorator;

@ccclass('Avatar')
export class Avatar extends Component {

    @property(userInfo)
    userInfo: userInfo = null;

    private dataManager: DataManager = null;
    start() {
        this.dataManager = DataManager.getInstance(); // 获取数据管理器实例，用于获取用户信息
        this.node.getComponent(Button).node.on('click', this.onClick, this);
        // 监听省份更新事件
        director.on('provinceUpdated', this.onUpdateProvince, this);
    }
    onUpdateProvince() {
        this.userInfo.set(this.dataManager.User.userId, this.dataManager.User.province, this.dataManager.User.username); // 显示个人信息面板，并传入用户ID
    }
    onClick() {
        // 打开个人信息
        this.userInfo.set(this.dataManager.User.userId, this.dataManager.User.province, this.dataManager.User.username); // 显示个人信息面板，并传入用户ID
        this.userInfo.node.active = true;

    }
    update(deltaTime: number) {
        
    }
}


