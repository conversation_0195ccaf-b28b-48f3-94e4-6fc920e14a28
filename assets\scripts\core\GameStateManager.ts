import { _decorator, Component, director, Node } from 'cc';
const { ccclass, property } = _decorator;

/*
    定义状态
    状态切换？事件通知？
*/

//定义状态
enum GameState {
    GameInit,
    OrderState,
    playerTurn,
    playerMove,
    playerStop,
    aiTurn,
    aiMove,
    aiStop,
    overState
}

export class GameStateManager {
    private static _instance: GameStateManager = null;

    public static getInstance(): GameStateManager {
        if (!this._instance) {
            this._instance = new GameStateManager();
        }
        return this._instance;
    }


    // 游戏初始化阶段
    gameInit() {
        console.log("游戏初始化阶段");
        // 发出通知
        director.emit("gameInit");

    }

    // 游戏决定顺序阶段
    orderState(playerNumber: Number, aiNumber: Number, Firster: string) {
        console.log("游戏决定顺序阶段");
        // 发出通知
        director.emit("orderState", playerNumber, aiNumber, Firster);
    }

    // 玩家回合
    playerTurn() {
        console.log("玩家回合");
        // 发出通知
        director.emit("playerTurn");
        // 监听玩家弹射事件
        director.on("marble-launched", this.playerMove, this);
    }
    // AI回合
    aiTurn() {
        console.log("AI回合");
        // 发出通知
        director.emit("aiTurn");

    }

    // 玩家移动阶段
    playerMove() {
        director.off("marble-launched", this.playerMove, this); // 移除弹射监听事件
        console.log("玩家移动阶段");
        // 发出通知
        director.emit("playerMove");
        // 监听停止事件
        director.on("AllMarbleStop", this.playerStop, this); // 监听停止事件
    }
    // AI移动阶段
    aiMove() {
        console.log("AI移动阶段");
        // 发出通知
        director.emit("aiMove");
        // 监听停止事件
        director.on("AllMarbleStop", this.aiStop, this); // 监听停止事件
    }

    // 玩家停止状态
    playerStop() {
        // 移除停止事件监听
        director.off("AllMarbleStop", this.playerStop, this);
        console.log("玩家停止状态");
        // 发出通知
        director.emit("playerStop");

    }
    // AI停止状态
    aiStop() {
        // 移除停止事件监听
        director.off("AllMarbleStop", this.aiStop, this);
        console.log("AI停止状态");
        // 发出通知
        director.emit("aiStop");
    }

    // 游戏结束状态
    gameOver(winner: string, rewardCount: number) {
        director.off("AllMarbleStop", this.aiStop, this);
        director.off("AllMarbleStop", this.playerStop, this);
        director.off("marble-launched", this.playerMove, this);

        console.log(`游戏结束状态 - 获胜者: ${winner}`);
        // 发出通知
        director.emit("gameOver", winner, rewardCount);
    }

    // 清理事件监听
    destroy() {
        director.off("AllMarbleStop", this.aiStop, this);
        director.off("AllMarbleStop", this.playerStop, this);
        director.off("marble-launched", this.playerMove, this);
    }
}
