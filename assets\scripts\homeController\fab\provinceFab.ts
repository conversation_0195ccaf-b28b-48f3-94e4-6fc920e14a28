import { _decorator, Color, Component, Label, Node, resources, Sprite, SpriteFrame, tween, UITransform, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('provinceFab')
export class provinceFab extends Component {
    start() {

    }

    set(province: string, rank: number, rate: string) {
        this.node.getChildByName("province").getComponent(Label).string = `${province}省`;
        this.node.getChildByName("rank").getComponent(Label).string = `第${rank.toString()}名`;
        this.node.getChildByName("rate").getComponent(Label).string = `胜率：${rate}`;
        // 添加sprite
        const layout1 = this.node.getChildByName('layout1');
        const layout2 = this.node.getChildByName('layout2');
        const layout3 = this.node.getChildByName('layout3');
        const layout4 = this.node.getChildByName('layout4');

        this.addSprites(layout1, 12);
        this.addSprites(layout2, 12);
        this.addSprites(layout3, 4);
        this.addSprites(layout4, 4);
    }

    addSprites(layout: Node, count: number) {
        for (let i = 0; i < count; i++) {
            const spriteNode = new Node(`sprite_${i}`);
            spriteNode.addComponent(UITransform);
            spriteNode.getComponent(UITransform).setContentSize(100, 100);
            spriteNode.addComponent(Sprite);

            const sprite = spriteNode.getComponent(Sprite);
            sprite.sizeMode = Sprite.SizeMode.CUSTOM;

            const randomNum = Math.floor(Math.random() * 4) + 1;
            resources.load(`skins/${randomNum}/spriteFrame`, SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    sprite.spriteFrame = spriteFrame;
                }
            });
            layout.addChild(spriteNode);

            // 添加动画效果
            this.addEffects(spriteNode, i);
        }
    }

    addEffects(node: Node, index: number) {
        // 初始缩放动画
        tween(node)
            .delay(index * 0.1)
            .to(0.4, { scale: new Vec3(1.2, 1.2, 1) })  // 0.2 → 0.4
            .to(0.2, { scale: new Vec3(0.8, 0.8, 1) })  // 0.1 → 0.2
            .to(0.3, { scale: new Vec3(1.1, 1.1, 1) })  // 0.15 → 0.3
            .to(0.3, { scale: new Vec3(1, 1, 1) })      // 0.15 → 0.3
            .call(() => {
                // 持续的弹跳动画（更慢）
                tween(node)
                    .repeatForever(
                        tween()
                            .to(0.6, { scale: new Vec3(1.1, 1.1, 1) })  // 0.3 → 0.6
                            .to(0.6, { scale: new Vec3(1, 1, 1) })      // 0.3 → 0.6
                    )
                    .start();

                // 持续的左右摇摆动画（更慢）
                tween(node)
                    .repeatForever(
                        tween()
                            .to(2 + Math.random(), { angle: 5 })   // 1 + random → 2 + random
                            .to(2 + Math.random(), { angle: -5 })  // 1 + random → 2 + random
                    )
                    .start();
            })
            .start();

        // 透明度闪烁动画
        const sprite = node.getComponent(Sprite);
        if (sprite) {
            tween(sprite)
                .delay(1 + Math.random() * 2)
                .repeatForever(
                    tween()
                        .to(0.8, { color: new Color(255, 255, 255, 180) })
                        .to(0.8, { color: new Color(255, 255, 255, 255) })
                )
                .start();
        }
    }

    update(deltaTime: number) {

    }
}


