import { _decorator, Component, Label, Sprite, resources, SpriteFrame } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('rankFab2')
export class rankFab2 extends Component {

    @property(Sprite)
    rankSprite: Sprite = null;

    @property(Label)
    rankLabel: Label = null;

    @property(Label)
    provinceLabel: Label = null;

    @property(Label)
    rateLabel: Label = null;

    @property(Label)
    countLabel: Label = null;

    start() {

    }

    set(rank: number, province: string, rate: string, count: string) {
        if (rank <= 3) {
            if (rank == 1) {
                resources.load("homeUI/rank1/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                    this.rankSprite.spriteFrame = spriteFrame;
                })
            }
            else if (rank == 2) {
                resources.load("homeUI/rank2/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                    this.rankSprite.spriteFrame = spriteFrame;
                })
            }
            else if (rank == 3) {
                resources.load("homeUI/rank3/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                    this.rankSprite.spriteFrame = spriteFrame;
                })
            }
            this.rankLabel.node.active = false;
        }
        else {
            this.rankLabel.string = rank.toString();
            this.rankSprite.node.active = false;
        }

        this.provinceLabel.string = province;
        this.rateLabel.string = rate;
        this.countLabel.string = count;
    }

    update(deltaTime: number) {

    }
}
