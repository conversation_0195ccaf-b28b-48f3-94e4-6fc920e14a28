import { _decorator, Component, Node, Sprite, resources, SpriteFrame, Widget } from 'cc';
const { ccclass, property } = _decorator;

/*
    根据是否签到了，来显示不同的状态
    两种签到
*/

@ccclass('rewardFeb')
export class rewardFeb extends Component {

    private isAdvanced: boolean = false;
    private status: number = 0;

    start() {
  
        this.node.on('click', this.onClick, this);
        // this.getComponentgetComponent(Widget).
    }

    onClick() {

    }

    init(isAdvanced: boolean, status: number) {
        this.isAdvanced = isAdvanced;
        this.status = status;
        this.setSpriteFrame();
    }

    setSpriteFrame() {
        if (this.isAdvanced) {
            if (this.status == 0) {
                resources.load("homeUI/奖励未激活背景_/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                    this.node.getComponent(Sprite).spriteFrame = spriteFrame;
                     
                })
            } else if (this.status == 1) {//已领取
                resources.load("homeUI/奖励激活背景_/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                    this.node.getComponent(Sprite).spriteFrame = spriteFrame;
                    this.node.getChildByName('active').active = true;
                })
            }
            this.node.getChildByName('video').active = true;
            
        }
        else {
            if (this.status == 0) {
                resources.load("homeUI/奖励未激活背景/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                    this.node.getComponent(Sprite).spriteFrame = spriteFrame;
                    
                })
            } else if (this.status == 1) {//已领取
                resources.load("homeUI/奖励激活背景/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                    this.node.getComponent(Sprite).spriteFrame = spriteFrame;
                    this.node.getChildByName('active').active = true;
                })
            }
            
        }
    }

    update(deltaTime: number) {

    }
}


