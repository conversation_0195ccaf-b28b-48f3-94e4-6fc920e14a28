[{"__type__": "cc.Prefab", "_name": "Obstacle3", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Obstacle3", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": -1968.654, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88Y8ODPxtIV6GMDBTH4hCZ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7d846b21-21af-40aa-b6c9-9f98d56cfc1a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "beq554Wp9G0L+jhhNnzFt6"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 0, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69NJFUqANDSbAimvnMEHzv"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.4, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": 16.810344827586206, "y": 50}, {"__type__": "cc.Vec2", "x": 14.655172413793096, "y": 49.19354838709677}, {"__type__": "cc.Vec2", "x": 11.206896551724135, "y": 46.37096774193547}, {"__type__": "cc.Vec2", "x": 8.620689655172413, "y": 45.564516129032256}, {"__type__": "cc.Vec2", "x": 8.189655172413786, "y": 44.758064516129025}, {"__type__": "cc.Vec2", "x": 0.8620689655172384, "y": 41.93548387096773}, {"__type__": "cc.Vec2", "x": -6.46551724137931, "y": 36.69354838709677}, {"__type__": "cc.Vec2", "x": -7.327586206896555, "y": 33.87096774193547}, {"__type__": "cc.Vec2", "x": -8.620689655172413, "y": 32.66129032258064}, {"__type__": "cc.Vec2", "x": -9.05172413793104, "y": 30.24193548387096}, {"__type__": "cc.Vec2", "x": -10.775862068965523, "y": 27.82258064516128}, {"__type__": "cc.Vec2", "x": -11.637931034482762, "y": 24.19354838709677}, {"__type__": "cc.Vec2", "x": -12.5, "y": 23.790322580645153}, {"__type__": "cc.Vec2", "x": -13.362068965517246, "y": 20.564516129032256}, {"__type__": "cc.Vec2", "x": -15.08620689655173, "y": 18.145161290322577}, {"__type__": "cc.Vec2", "x": -15.08620689655173, "y": 16.532258064516128}, {"__type__": "cc.Vec2", "x": -16.810344827586206, "y": 13.709677419354833}, {"__type__": "cc.Vec2", "x": -17.672413793103452, "y": 10.08064516129032}, {"__type__": "cc.Vec2", "x": -18.965517241379313, "y": 8.467741935483865}, {"__type__": "cc.Vec2", "x": -19.396551724137932, "y": 5.6451612903225765}, {"__type__": "cc.Vec2", "x": -21.120689655172416, "y": 3.225806451612897}, {"__type__": "cc.Vec2", "x": -21.551724137931036, "y": 0.40322580645160855}, {"__type__": "cc.Vec2", "x": -22.844827586206897, "y": -0.8064516129032313}, {"__type__": "cc.Vec2", "x": -23.27586206896552, "y": -4.032258064516135}, {"__type__": "cc.Vec2", "x": -31.3, "y": -20.4}, {"__type__": "cc.Vec2", "x": -33.62068965517241, "y": -33.064516129032256}, {"__type__": "cc.Vec2", "x": -31.896551724137932, "y": -36.29032258064516}, {"__type__": "cc.Vec2", "x": -26.724137931034484, "y": -36.29032258064516}, {"__type__": "cc.Vec2", "x": -26.724137931034484, "y": -37.096774193548384}, {"__type__": "cc.Vec2", "x": -24.56896551724138, "y": -37.5}, {"__type__": "cc.Vec2", "x": -22.413793103448278, "y": -39.11290322580645}, {"__type__": "cc.Vec2", "x": -19.827586206896555, "y": -39.91935483870968}, {"__type__": "cc.Vec2", "x": -18.965517241379313, "y": -41.12903225806451}, {"__type__": "cc.Vec2", "x": -15.517241379310349, "y": -42.33870967741935}, {"__type__": "cc.Vec2", "x": -15.08620689655173, "y": -43.145161290322584}, {"__type__": "cc.Vec2", "x": -13.362068965517246, "y": -43.54838709677419}, {"__type__": "cc.Vec2", "x": -12.5, "y": -44.75806451612903}, {"__type__": "cc.Vec2", "x": -9.913793103448278, "y": -45.564516129032256}, {"__type__": "cc.Vec2", "x": -8.189655172413794, "y": -47.17741935483871}, {"__type__": "cc.Vec2", "x": -4.310344827586206, "y": -47.983870967741936}, {"__type__": "cc.Vec2", "x": -3.879310344827587, "y": -48.79032258064516}, {"__type__": "cc.Vec2", "x": -1.724137931034484, "y": -49.193548387096776}, {"__type__": "cc.Vec2", "x": -0.8620689655172455, "y": -50}, {"__type__": "cc.Vec2", "x": 3.0172413793103416, "y": -50}, {"__type__": "cc.Vec2", "x": 4.310344827586206, "y": -49.193548387096776}, {"__type__": "cc.Vec2", "x": 20.689655172413794, "y": -49.193548387096776}, {"__type__": "cc.Vec2", "x": 25.86206896551724, "y": -46.774193548387096}, {"__type__": "cc.Vec2", "x": 26.724137931034477, "y": -45.564516129032256}, {"__type__": "cc.Vec2", "x": 27.586206896551715, "y": -45.564516129032256}, {"__type__": "cc.Vec2", "x": 35.5, "y": -38.1}, {"__type__": "cc.Vec2", "x": 41.37931034482759, "y": -16.12903225806452}, {"__type__": "cc.Vec2", "x": 40.948275862068954, "y": -11.693548387096776}, {"__type__": "cc.Vec2", "x": 35.77586206896551, "y": 7.661290322580641}, {"__type__": "cc.Vec2", "x": 32.32758620689654, "y": 27.419354838709666}, {"__type__": "cc.Vec2", "x": 30.603448275862064, "y": 33.46774193548387}, {"__type__": "cc.Vec2", "x": 30.603448275862064, "y": 39.516129032258064}, {"__type__": "cc.Vec2", "x": 29.741379310344826, "y": 43.951612903225794}, {"__type__": "cc.Vec2", "x": 28.017241379310335, "y": 45.564516129032256}, {"__type__": "cc.Vec2", "x": 27.586206896551715, "y": 47.177419354838705}, {"__type__": "cc.Vec2", "x": 25.86206896551724, "y": 47.983870967741936}, {"__type__": "cc.Vec2", "x": 25.43103448275862, "y": 49.19354838709677}, {"__type__": "cc.Vec2", "x": 23.27586206896551, "y": 50}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cif+74kBKo6wghXNRKIuo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "949h2icwdJ8ZMPT6ByKnEi", "instance": null, "targetOverrides": null}]