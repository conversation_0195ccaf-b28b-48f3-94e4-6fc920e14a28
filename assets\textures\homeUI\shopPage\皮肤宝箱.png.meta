{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "2a0b70f7-249c-4584-a35a-2e17a5f9be28", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "2a0b70f7-249c-4584-a35a-2e17a5f9be28@6c48a", "displayName": "皮肤宝箱", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "2a0b70f7-249c-4584-a35a-2e17a5f9be28", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "2a0b70f7-249c-4584-a35a-2e17a5f9be28@f9941", "displayName": "皮肤宝箱", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 502, "height": 466, "rawWidth": 502, "rawHeight": 466, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-251, -233, 0, 251, -233, 0, -251, 233, 0, 251, 233, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 466, 502, 466, 0, 0, 502, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-251, -233, 0], "maxPos": [251, 233, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "2a0b70f7-249c-4584-a35a-2e17a5f9be28@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "2a0b70f7-249c-4584-a35a-2e17a5f9be28@6c48a"}}