
import { _decorator, Component, Node, find, Camera, director, random } from 'cc';
const { ccclass, property } = _decorator;
import { TargetArea } from '../gameController/physics/TargetArea';
import { PlayeMarble } from '../gameController/physics/PlayeMarble';
import { Floor } from '../gameController/physics/Floor';
import { AiMarble } from '../gameController/physics/AiMarble';
import { AimLine } from '../gameController/physics/AimLine';
import { PowerBar } from '../gameController/physics/PowerBar';
import { Finger } from '../gameController/physics/Finger';
import { CameraController } from '../gameController/ui/CameraController';
import { GameStateManager } from './GameStateManager';
import { DataManager } from '../Managers/DataManager';
import { UIManager } from '../Managers/GameUIManager';
/*
    实现游戏过程管理
*/

@ccclass('GameManager')
export class GameManager extends Component {


    @property(TargetArea)
    targetArea: TargetArea = null

    @property(PlayeMarble)
    playerMarble: PlayeMarble = null

    @property(AiMarble)
    aiMarble: AiMarble = null

    @property(CameraController)
    cameraController: CameraController = null;

    // 游戏状态管理器
    private stateManager: GameStateManager = null;

    // 游戏数据管理器
    private dataManager: DataManager = null;




    onLoad() {
        // 初始化非Component管理器
        this.stateManager = GameStateManager.getInstance();

        this.dataManager = DataManager.getInstance();
        this.dataManager.initialize_game();


    }

    start() {
        console.log("开始游戏");

        // 重置游戏数据
        this.dataManager.resetGameStats();

        // 监听UIManager初始化完成
        director.on("uiManagerInited", this.inited, this);

        // 初始化游戏
        this.stateManager.gameInit();

    }

    inited() {
        console.log("UIManager初始化完成");
        director.off("uiManagerInited", this.inited, this);

        director.on("orderStateInited", this.orderStateInited, this);

        // 决定顺序
        this.stateManager.orderState(...this.decideOrder());

    }

    orderStateInited() {
        console.log("顺序状态初始化完成");
        director.off("orderStateInited", this.orderStateInited, this);

        director.on('playerTurn', this.onPlayerTurn, this);
        director.on('aiTurn', this.onAiTurn, this);
        director.on('playerTimeOut', this.onPlayerTimeOut, this);

        // 开始回合
        if (this.dataManager.currentPlayer == "player") {
            this.stateManager.playerTurn();
        }
        else {
            this.stateManager.aiTurn();
        }
    }
    // 玩家回合
    onPlayerTurn() {
        // 设置相机控制器目标为玩家的球体节点
        director.on('choosefail', this.onChooseFail, this);
        this.cameraController.setTarget(this.playerMarble.node);
        // 监听是否进入停止阶段
        director.on('playerStop', this.onPlayerStop, this);
    };
    // AI回合
    onAiTurn() {
        // 设置相机控制器目标为AI的球体节点
        this.cameraController.setTarget(this.aiMarble.node);
        // 监听是否进入停止阶段
        director.on('aiStop', this.onAiStop, this);
        console.log("AI马上发射");
        // AI弹射
        setTimeout(() => {
            this.aiMarble.smartLaunch(this.targetArea, this.playerMarble.node);
            this.stateManager.aiMove();
        }, Math.floor(Math.random() * 2000) + 1000);
    }
    onChooseFail() {
        // 清理事件监听
        director.off('choosefail', this.onChooseFail, this);
        // 直接AI赢
        this.gameOver("ai");
    }
    onPlayerStop() {
        // 清理事件监听
        director.off('playerStop', this.onPlayerStop, this);
        director.off('choosefail', this.onChooseFail, this);
        // 处理输赢还是继续
        if (this.targetArea.judgeInArea(this.playerMarble.node)) {
            console.log("玩家失败 - 弹珠进入目标区域");
            // 游戏结束，AI获胜
            this.gameOver("ai");
            return;
        }
        else if (this.targetArea.judgeInArea(this.aiMarble.node)) {
            console.log("AI失败 - 弹珠进入目标区域");
            // 游戏结束，玩家获胜
            this.gameOver("player");
            return;
        }

        // 检查是否击出了目标弹珠
        const hitMarbles = this.targetArea.getOutTarget();
        if (hitMarbles.length > 0) {
            console.log(`玩家击出了${hitMarbles.length}颗目标弹珠`);
            this.dataManager.addHitMarbles("player", hitMarbles.length);
            this.stateManager.playerTurn();
            if (this.targetArea.checkALLTargetMarblesOut()) {
                if (this.dataManager.compareMarbles() == "player") {

                    this.gameOver("player");
                }
                else if (this.dataManager.compareMarbles() == "ai") {
                    this.gameOver("ai");
                }
            }

        }
        else {
            this.dataManager.switchPlayer();
            this.stateManager.aiTurn();
        }

    }


    // 玩家超时处理
    onPlayerTimeOut() {
        console.log("玩家超时，强制跳过回合");
        // 直接切换到AI回合
        this.dataManager.switchPlayer();
        this.stateManager.aiTurn();
    }

    onAiStop() {
        // 清理事件监听
        director.off('aiStop', this.onAiStop, this);

        // 处理输赢还是继续
        if (this.targetArea.judgeInArea(this.aiMarble.node)) {
            console.log("AI失败 - 弹珠进入目标区域");
            // 游戏结束，玩家获胜
            this.gameOver("player");
            return;
        }
        else if (this.targetArea.judgeInArea(this.playerMarble.node)) {
            console.log("玩家失败 - 弹珠进入目标区域");
            // 游戏结束，AI获胜
            this.gameOver("ai");
            return;
        }

        // 检查是否击出了目标弹珠
        const hitMarbles = this.targetArea.getOutTarget();
        if (hitMarbles.length > 0) {
            console.log(`AI击出了${hitMarbles.length}颗目标弹珠`);
            this.dataManager.addHitMarbles("ai", hitMarbles.length);
            this.stateManager.aiTurn();
            if (this.targetArea.checkALLTargetMarblesOut()) {   
                if (this.dataManager.compareMarbles() == "ai") {
                    this.gameOver("ai");
                }
                else if (this.dataManager.compareMarbles() == "player") {
                    this.gameOver("player");
                }
            }
        }
        // 检查所有目标弹珠是否都被击出

        else {
            console.log("AI回合结束，切换到玩家回合");
            this.dataManager.switchPlayer();
            this.stateManager.playerTurn();
        }
    }

    // 决定顺序
    decideOrder(): [number, number, string] {
        const diceValue1 = this.playerMarble.rollDice();
        const diceValue2 = this.aiMarble.rollDice();

        return [diceValue1, diceValue2, diceValue1 > diceValue2 ? "player" : "ai"];
    }

    // 游戏结束
    gameOver(winner: string) {
        if (winner === "player") {
            this.dataManager.settleGame('win');
        }
        else if (winner === "ai") {
            this.dataManager.settleGame('lose');
        }
        else {
            this.dataManager.settleGame('tie');
        }
        

        console.log(`游戏结束！获胜者: ${winner}`);
        console.log(`玩家击出: ${this.dataManager.playerMarblesHit}颗`);
        console.log(`AI击出: ${this.dataManager.aiMarblesHit}颗`);
        console.log(`战利品: ${this.dataManager.playerReward}颗`);
        console.log(`最终收益: ${this.dataManager.playerFinalReward}颗`);

        // 清理所有事件监听
        director.off('playerTurn', this.onPlayerTurn, this);
        director.off('aiTurn', this.onAiTurn, this);
        director.off('playerStop', this.onPlayerStop, this);
        director.off('aiStop', this.onAiStop, this);
        director.off('orderStateInited', this.orderStateInited, this);
        director.off('playerTimeOut', this.onPlayerTimeOut, this);

        // 通知状态管理器游戏结束
        this.stateManager.gameOver(winner, this.dataManager.playerReward);
    }

    onDestroy() {
        // 清理管理器
        if (this.stateManager) {
            this.stateManager.destroy();
        }
        if (this.dataManager) {
            this.dataManager.destroy();
        }
    }

    update(deltaTime: number) {

    }
}


