import { _decorator, Component, No<PERSON>, <PERSON><PERSON>, director, Label } from 'cc';
import { DataManager } from '../../Managers/DataManager';
import { loading } from '../loading';
import { ButtonAnimationUtils } from '../../Utils/ButtonAnimationUtils';
import { activity } from '../activity';
import { ranklist } from '../ranklist';
import { beginPresent } from '../beginPresent';
const { ccclass, property } = _decorator;

/*
    战斗页面管理器 - 管理游戏难度选择和功能按钮

    主要功能：
    1. 游戏难度选择：初级、中级、高级
    2. 左侧功能栏：福利、背包、分享、转盘
    3. 右侧功能栏：公告、设置、活动、排行榜、社区
    4. 统一按钮动画和事件管理

    使用方式：
    - 挂载到主页Canvas的BattlePage节点上
    - 在编辑器中配置各按钮节点引用
    - 配置loading页面组件引用
*/
@ccclass('BattlePage')
export class BattlePage extends Component {

    // === 加载页面引用 ===
    @property(loading)
    loadingPage: loading = null;

    // === 主内容区按钮（游戏难度选择） ===
    @property(Node)
    primaryButton: Node = null;

    @property(Node)
    middleButton: Node = null;

    @property(Node)
    seniorButton: Node = null;

    // === 左侧边栏按钮 ===
    @property(Node)
    benefitButton: Node = null;

    @property(Node)
    backpackButton: Node = null;

    @property(Node)
    shareButton: Node = null;

    @property(Node)
    rouletteButton: Node = null;

    // === 右侧边栏按钮 ===
    @property(Node)
    noticeButton: Node = null;

    @property(Node)
    settingButton: Node = null;

    @property(Node)
    activityButton: Node = null;

    @property(Node)
    rankingButton: Node = null;

    @property(Node)
    communityButton: Node = null;

    @property(Label)
    bannerInfo: Label = null;

    onLoad() {
        this.setupAllButtons();
    }

    start() {
        this.setupButtonEvents();
        
    }

    /**
     * 设置所有按钮的动画效果
     */
    private setupAllButtons() {
        const allButtons = [
            this.primaryButton, this.middleButton, this.seniorButton,
            this.benefitButton, this.backpackButton, this.shareButton, this.rouletteButton,
            this.noticeButton, this.settingButton, this.activityButton, this.rankingButton, this.communityButton
        ];

        allButtons.forEach(button => {
            if (button) {
                ButtonAnimationUtils.setupButtonInteraction(button);
            }
        });
    }

    /**
     * 设置所有按钮的点击事件
     */
    private setupButtonEvents() {
        this.setupMainContentEvents();
        this.setupSidebarEvents();
    }

    /**
     * 设置游戏难度选择按钮事件
     */
    private setupMainContentEvents() {
        if (this.primaryButton) {
            this.primaryButton.on(Node.EventType.TOUCH_END, () => this.onLevelClick('primary'), this);
        }
        if (this.middleButton) {
            this.middleButton.on(Node.EventType.TOUCH_END, () => this.onLevelClick('middle'), this);
        }
        if (this.seniorButton) {
            this.seniorButton.on(Node.EventType.TOUCH_END, () => this.onLevelClick('senior'), this);
        }
    }

    /**
     * 设置侧边栏按钮事件
     */
    private setupSidebarEvents() {
        if (this.benefitButton) {
            this.benefitButton.on(Node.EventType.TOUCH_END, this.onBenefitClick, this);
        }
        if (this.backpackButton) {
            this.backpackButton.on(Node.EventType.TOUCH_END, this.onBackpackClick, this);
        }
        if (this.shareButton) {
            this.shareButton.on(Node.EventType.TOUCH_END, this.onShareClick, this);
        }
        if (this.rouletteButton) {
            this.rouletteButton.on(Node.EventType.TOUCH_END, this.onRouletteClick, this);
        }
        if (this.noticeButton) {
            this.noticeButton.on(Node.EventType.TOUCH_END, this.onNoticeClick, this);
        }
        if (this.settingButton) {
            this.settingButton.on(Node.EventType.TOUCH_END, this.onSettingClick, this);
        }
        if (this.activityButton) {
            this.activityButton.on(Node.EventType.TOUCH_END, this.onActivityClick, this);
        }
        if (this.rankingButton) {
            this.rankingButton.on(Node.EventType.TOUCH_END, this.onRankingClick, this);
        }
        if (this.communityButton) {
            this.communityButton.on(Node.EventType.TOUCH_END, this.onCommunityClick, this);
        }
    }

    /**
     * 处理游戏难度选择
     */
    private onLevelClick(level: 'primary' | 'middle' | 'senior') {
        console.log(`选择游戏难度: ${level}`);

        const dataManager = DataManager.getInstance();
        dataManager.setLevel(level);

        this.loadingPage.showAndLoadGame(
            dataManager.User?.username,
            "AI对手",
            dataManager.User?.level,
            18
        );
    }

    private onBenefitClick() {
        console.log('点击福利按钮');
        this.showBenefitPanel();
    }

    private onBackpackClick() {
        console.log('点击背包按钮');
        this.showBackpackPanel();
    }

    private onShareClick() {
        console.log('点击分享按钮');
        this.shareGame();
    }

    private onRouletteClick() {
        console.log('点击转盘按钮');
        this.showRoulettePanel();
    }

    private onNoticeClick() {
        console.log('点击公告按钮');
        this.showNoticePanel();
    }

    private onSettingClick() {
        console.log('点击设置按钮');
        this.showSettingPanel();
    }

    private onActivityClick() {
        console.log('点击活动按钮');
        this.showActivityPanel();
    }

    private onRankingClick() {
        console.log('点击排行榜按钮');
        this.showRankingPanel();
    }

    private onCommunityClick() {
        console.log('点击社区按钮');
        this.showCommunityPanel();
    }

    private showBenefitPanel() {
        const benefitNode = this.node.scene.getChildByName('Canvas').getChildByName('beginPresent');
        if (benefitNode) {
            benefitNode.active = true;
        }
    }

    private showBackpackPanel() {
        const backpackNode = this.node.scene.getChildByName('Canvas').getChildByName('backpack');
        if (backpackNode) {
            backpackNode.active = true;
        }
    }

    private shareGame() {
        console.log('分享游戏');
    }

    private showRoulettePanel() {
        const turntableNode = this.node.scene.getChildByName('Canvas').getChildByName('turntable');
        if (turntableNode) {
            turntableNode.active = true;
        }
    }

    private showNoticePanel() {
        const noticeNode = this.node.scene.getChildByName('Canvas').getChildByName('notice');
        if (noticeNode) {
            noticeNode.active = true;
        }
    }

    private showSettingPanel() {
        const settingNode = this.node.scene.getChildByName('Canvas').getChildByName('setting');
        if (settingNode) {
            settingNode.active = true;
        }
    }

    private showActivityPanel() {
        const activityNode = this.node.scene.getChildByName('Canvas').getChildByName('activity');
        if (activityNode) {
            activityNode.active = true;
        }
    }

    private showRankingPanel() {
        const rankingNode = this.node.scene.getChildByName('Canvas').getChildByName('ranklist');
        if (rankingNode) {
            rankingNode.active = true;
        }
    }

    private showCommunityPanel() {
        console.log('显示社区界面');
    }

    onDestroy() {
        ButtonAnimationUtils.clearAllAnimations();
        this.cleanupEvents();
    }

    private cleanupEvents() {
        const allButtons = [
            this.primaryButton, this.middleButton, this.seniorButton,
            this.benefitButton, this.backpackButton, this.shareButton, this.rouletteButton,
            this.noticeButton, this.settingButton, this.activityButton, this.rankingButton, this.communityButton
        ];

        allButtons.forEach(button => {
            if (button && button.isValid) {
                button.off(Node.EventType.TOUCH_END);
                ButtonAnimationUtils.removeButtonInteraction(button);
            }
        });
    }
}
