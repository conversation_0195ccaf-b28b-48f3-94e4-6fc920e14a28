import { _decorator, Button, Component, Node, Prefab, resources, Sprite, SpriteFrame, JsonAsset, instantiate, UITransform, ScrollView } from 'cc';
import { rankFab } from './fab/rankFab';
import { rankFab2 } from './fab/rankFab2';
import { rankFab3 } from './fab/rankFab3';
const { ccclass, property } = _decorator;

/*
    排行榜系统组件 - 管理多种排行榜展示和切换

    主要功能：
    1. 排行榜分类：好友榜、世界榜、个人榜三种类型
    2. 数据展示：加载并显示排行榜数据和玩家信息
    3. 榜单切换：支持不同排行榜类型的切换显示
    4. 排名显示：展示前三名特殊头像和详细排名列表

    使用方式：
    - 通过BattlePage的排行榜按钮激活显示
    - 自动加载排行榜数据文件
    - 支持点击切换不同类型的排行榜
*/
@ccclass('ranklist')
export class ranklist extends Component {

    @property(Node)
    btnFriend: Node = null;

    @property(Node)
    btnWorld: Node = null;

    @property(Node)
    btnPerson: Node = null;

    @property(Node)
    head1: Node = null;

    @property(Node)
    head2: Node = null;

    @property(Node)
    head3: Node = null;

    @property(Prefab)
    rankfab: Prefab = null;

    @property(Prefab)
    rankfab2: Prefab = null;

    @property(Prefab)
    rankfab3: Prefab = null;

    @property(Node)
    scrollContent: Node = null;

    @property(Button)
    closeBtn: Button = null;

    private rankData: any = null;
    private currentRankType: string = "friend";

    start() {
        this.loadRankData();
        this.btnFriend.getComponent(Button).node.on('click', this.onBtnFriend, this);
        this.btnWorld.getComponent(Button).node.on('click', this.onBtnWorld, this);
        this.btnPerson.getComponent(Button).node.on('click', this.onBtnPerson, this);
        this.closeBtn.node.on('click', this.closeRankList, this);
        this.onBtnWorld();
    }

    // 加载排行榜数据
    loadRankData() {
        resources.load('data/ranklist', JsonAsset, (err, asset) => {
            if (!err) {
                this.rankData = asset.json;
                this.showRegionRank();
            }
        });
    }

    onBtnFriend() {
        this.currentRankType = "friend";
        this.updateButtonStates(0); // 0 表示好友榜按钮
        this.head1.active = true;
        this.head2.active = false;
        this.head3.active = false;
        this.showFriendRank();
    }

    onBtnWorld() {
        this.currentRankType = "region";
        this.updateButtonStates(1); // 1 表示地区榜按钮
        this.head1.active = false;
        this.head2.active = true;
        this.head3.active = false;
        this.showRegionRank();
    }

    onBtnPerson() {
        this.currentRankType = "personal";
        this.updateButtonStates(2); // 2 表示个人榜按钮
        this.head1.active = false;
        this.head2.active = false;
        this.head3.active = true;
        this.showPersonalRank();
    }

    // 更新按钮状态 - 动态切换底版
    updateButtonStates(selectedIndex: number) {
        const buttons = [this.btnFriend, this.btnWorld, this.btnPerson];

        for (let i = 0; i < buttons.length; i++) {
            const button = buttons[i];
            if (i === selectedIndex) {
                // 选中状态 - 加载选中的底版
                resources.load('homeUI/选中/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                    if (!err && button) {
                        button.getComponent(Sprite).spriteFrame = spriteFrame;
                    }
                });
            } else {
                // 未选中状态 - 加载默认底版
                resources.load('homeUI/未选中/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                    if (!err && button) {
                        button.getComponent(Sprite).spriteFrame = spriteFrame;
                    }
                });
            }
        }
    }

    // 清空滚动视图内容
    clearScrollContent() {
        if (this.scrollContent) {
            this.scrollContent.removeAllChildren();
        }
    }

    // 显示好友榜
    showFriendRank() {
        if (!this.rankData || !this.rankfab) return;
        this.clearScrollContent();

        const friendData = this.rankData.friendRank;
        for (let i = 0; i < friendData.length; i++) {
            const item = friendData[i];
            const rankNode = instantiate(this.rankfab);
            const rankComponent = rankNode.getComponent(rankFab);
            if (rankComponent) {
                rankComponent.set(item.rank, item.name, item.dan);
            }
            this.scrollContent.addChild(rankNode);
        }

        this.scrollToTop();
    }

    // 显示地区榜
    showRegionRank() {
        if (!this.rankData || !this.rankfab2) return;
        this.clearScrollContent();

        const regionData = this.rankData.regionRank;
        for (let i = 0; i < regionData.length; i++) {
            const item = regionData[i];
            const rankNode = instantiate(this.rankfab2);
            const rankComponent = rankNode.getComponent(rankFab2);
            if (rankComponent) {
                rankComponent.set(item.rank, item.province, item.rate, item.count);
            }
            this.scrollContent.addChild(rankNode);
        }
        this.scrollToTop();
    }

    // 显示个人榜
    showPersonalRank() {
        if (!this.rankData || !this.rankfab3) return;
        this.clearScrollContent();

        const personalData = this.rankData.personalRank;
        for (let i = 0; i < personalData.length; i++) {
            const item = personalData[i];
            const rankNode = instantiate(this.rankfab3);
            const rankComponent = rankNode.getComponent(rankFab3);
            if (rankComponent) {
                rankComponent.set(item.rank, item.name, item.count);
            }
            this.scrollContent.addChild(rankNode);
        }
        this.scrollToTop();
    }

    // 滚动到顶部
    scrollToTop() {
        const scrollView = this.scrollContent.parent.parent.getComponent(ScrollView);
        if (scrollView) {
            scrollView.scrollToTop(0);
        }
    }

    closeRankList() {
        this.node.active = false;
    }

    update(deltaTime: number) {

    }
}


