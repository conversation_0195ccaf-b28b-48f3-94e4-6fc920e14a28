import { _decorator, Component, Node, resources, JsonAsset, instantiate, ScrollView, UITransform, Prefab, Vec3, Sprite, SpriteFrame, <PERSON><PERSON>, director } from 'cc';
import { cardFeb } from '../fab/cardFab';
import { DataManager } from '../../Managers/DataManager';
const { ccclass, property } = _decorator;

/*
    由数据渲染不同特效展示卡片,一行四个
*/

@ccclass('effectPage')
export class effectPage extends Component {

    // 已解锁皮肤的滚动视图
    @property(ScrollView)
    ScrollView: ScrollView = null;

    // 卡片预制体
    @property(Prefab)
    card: Prefab = null;

    @property(Button)
    effect1Btn: Button = null;

    @property(Button)
    effect2Btn: Button = null;

    @property(Button)
    effect3Btn: Button = null;

    private launchEffectsData: any[] = [];
    private collisionEffectsData: any[] = [];
    private trailEffectsData: any[] = [];
    private currentEffectType: string = 'launch'; // 当前显示的特效类型
    private dataManager: DataManager = null;

    start() {
        this.dataManager = DataManager.getInstance();
        this.loadAllEffectsData();
        this.effect1Btn.node.on('click', this.onClickEffect1, this);
        this.effect2Btn.node.on('click', this.onClickEffect2, this);
        this.effect3Btn.node.on('click', this.onClickEffect3, this);

        // 监听装备变化事件，刷新UI显示
        director.on('equipmentChanged', this.onEquipmentChanged, this);

        // 默认选中发射特效
        this.onClickEffect1();
    }

    onClickEffect1() {
        this.currentEffectType = 'launch';
        this.updateButtonStates();
        this.renderEffectCards();
    }

    onClickEffect2() {
        this.currentEffectType = 'collision';
        this.updateButtonStates();
        this.renderEffectCards();
    }

    onClickEffect3() {
        this.currentEffectType = 'trail';
        this.updateButtonStates();
        this.renderEffectCards();
    }

    updateButtonStates() {
        // 重置所有按钮状态
        resources.load('homeUI/effect未选中/spriteFrame', SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                this.effect1Btn.node.getComponent(Sprite).spriteFrame = spriteFrame;
                this.effect2Btn.node.getComponent(Sprite).spriteFrame = spriteFrame;
                this.effect3Btn.node.getComponent(Sprite).spriteFrame = spriteFrame;
            }
        });

        // 设置当前选中按钮状态
        let selectedBtn: Button;
        if (this.currentEffectType === 'launch') selectedBtn = this.effect1Btn;
        else if (this.currentEffectType === 'collision') selectedBtn = this.effect2Btn;
        else if (this.currentEffectType === 'trail') selectedBtn = this.effect3Btn;

        if (selectedBtn) {
            resources.load('homeUI/effect选中/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    selectedBtn.node.getComponent(Sprite).spriteFrame = spriteFrame;
                }
            });
        }
    }

    loadAllEffectsData() {
        // 并行加载三种特效数据
        Promise.all([
            this.loadJsonAsset('data/launchEffects'),
            this.loadJsonAsset('data/collisionEffects'),
            this.loadJsonAsset('data/trailEffects')
        ]).then(([launchAsset, collisionAsset, trailAsset]) => {
            this.launchEffectsData = (launchAsset as JsonAsset).json.launchEffects;
            this.collisionEffectsData = (collisionAsset as JsonAsset).json.collisionEffects;
            this.trailEffectsData = (trailAsset as JsonAsset).json.trailEffects;
            this.renderEffectCards();
        }).catch(error => {
            console.error("加载特效数据失败:", error);
        });
    }

    loadJsonAsset(path: string): Promise<JsonAsset> {
        return new Promise((resolve, reject) => {
            resources.load(path, JsonAsset, (err, asset) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(asset);
                }
            });
        });
    }

    renderEffectCards() {
        if (!this.dataManager.User) {
            console.error("用户数据未初始化");
            return;
        }

        // 根据当前选择的特效类型获取数据
        let currentEffectsData: any[] = [];
        let ownedEffects: number[] = [];
        let currentEffectId: number = 0;

        switch (this.currentEffectType) {
            case 'launch':
                currentEffectsData = this.launchEffectsData;
                ownedEffects = this.dataManager.User.ownedLaunchEffects;
                currentEffectId = this.dataManager.User.currentLaunchEffect;
                break;
            case 'collision':
                currentEffectsData = this.collisionEffectsData;
                ownedEffects = this.dataManager.User.ownedCollisionEffects;
                currentEffectId = this.dataManager.User.currentCollisionEffect;
                break;
            case 'trail':
                currentEffectsData = this.trailEffectsData;
                ownedEffects = this.dataManager.User.ownedTrailEffects;
                currentEffectId = this.dataManager.User.currentTrailEffect;
                break;
        }

        const unlockedEffects: any[] = [];
        const lockedEffects: any[] = [];

        // 分类特效
        currentEffectsData.forEach(effect => {
            if (ownedEffects.indexOf(effect.effectId) !== -1) {
                unlockedEffects.push(effect);
            } else {
                lockedEffects.push(effect);
            }
        });

        // 合并特效数组，已解锁在前
        const allEffects = [...unlockedEffects, ...lockedEffects];

        // 在同一个滚动视图中渲染所有特效
        this.createEffectCards(allEffects, this.ScrollView, unlockedEffects.length, currentEffectId);
    }

    createEffectCards(effects: any[], scrollView: ScrollView, unlockedCount: number, currentEffectId: number) {
        const contentNode = scrollView.content;
        
        // 清空现有卡片
        contentNode.removeAllChildren();

        const cardsPerRow = 4;
        const cardWidth = 247;
        const cardHeight = 324;
        const spacingX = 10;
        const spacingY = 20;
        const paddingTop = 20;  
        const paddingLeft = 40;
        const titleHeight = 70; // 标题行高度

        // 计算已解锁和未解锁的行数
        const unlockedRows = Math.ceil(unlockedCount / cardsPerRow);
        const lockedRows = Math.ceil((effects.length - unlockedCount) / cardsPerRow);

        // 获取ScrollView的宽度，使content宽度与ScrollView一致
        const scrollViewWidth = scrollView.node.getComponent(UITransform).contentSize.width;

        // 计算实际需要的内容高度
        let contentHeight = paddingTop * 2; // 上下padding

        // 如果有已解锁特效，添加标题和卡片高度
        if (unlockedCount > 0) {
            contentHeight += titleHeight + spacingY; // 已解锁标题
            contentHeight += unlockedRows * cardHeight + Math.max(0, unlockedRows - 1) * spacingY; // 已解锁卡片
            if (effects.length > unlockedCount) {
                contentHeight += spacingY; // 标题间距（只有在有未解锁特效时才添加）
            }
        }

        // 如果有未解锁特效，添加标题和卡片高度
        if (effects.length > unlockedCount) {
            contentHeight += titleHeight + spacingY; // 未解锁标题
            contentHeight += lockedRows * cardHeight + Math.max(0, lockedRows - 1) * spacingY; // 未解锁卡片
        }

        // 设置content大小和锚点
        const uiTransform = contentNode.getComponent(UITransform);
        uiTransform.setContentSize(scrollViewWidth, contentHeight);
        uiTransform.setAnchorPoint(0.5, 1);

        // 调试信息
        console.log(`特效页面 - 内容高度: ${contentHeight}, 滚动视图高度: ${scrollView.node.getComponent(UITransform).contentSize.height}`);
        console.log(`已解锁特效数量: ${unlockedCount}, 总特效数量: ${effects.length}`);

        let currentY = -paddingTop;

        // 创建已解锁标题
        if (unlockedCount > 0) {
            const unlockedTitle = new Node("UnlockedTitle");
            const titleSprite = unlockedTitle.addComponent(Sprite);

            // 加载已解锁标题的sprite图片
            resources.load('homeUI/已解锁/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    titleSprite.spriteFrame = spriteFrame;
                }
            });

            unlockedTitle.setPosition(0, currentY - titleHeight / 2);
            contentNode.addChild(unlockedTitle);
            currentY -= titleHeight + spacingY;

            // 创建已解锁卡片
            for (let i = 0; i < unlockedCount; i++) {
                const cardNode = instantiate(this.card);
                const cardComponent = cardNode.getComponent(cardFeb);

                const row = Math.floor(i / cardsPerRow);
                const col = i % cardsPerRow;

                const startX = -scrollViewWidth / 2 + paddingLeft + cardWidth / 2;
                const x = startX + col * (cardWidth + spacingX);
                const y = currentY - row * (cardHeight + spacingY) - cardHeight / 2;

                cardNode.setPosition(new Vec3(x, y, 0));
                const isCurrent = effects[i].effectId === currentEffectId;
                cardComponent.init(effects[i], false, effects[i].rarity, isCurrent);
                contentNode.addChild(cardNode);
            }

            // 更新Y位置到已解锁区域后
            const unlockedRows = Math.ceil(unlockedCount / cardsPerRow);
            currentY -= unlockedRows * (cardHeight + spacingY);
        }

        // 创建未解锁标题
        const lockedCount = effects.length - unlockedCount;
        if (lockedCount > 0) {
            const lockedTitle = new Node("LockedTitle");
            const titleSprite = lockedTitle.addComponent(Sprite);

            // 加载未解锁标题的sprite图片
            resources.load('homeUI/未解锁/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    titleSprite.spriteFrame = spriteFrame;
                }
            });

            lockedTitle.setPosition(0, currentY - titleHeight / 2);
            contentNode.addChild(lockedTitle);
            currentY -= titleHeight + spacingY;

            // 创建未解锁卡片
            for (let i = 0; i < lockedCount; i++) {
                const cardNode = instantiate(this.card);
                const cardComponent = cardNode.getComponent(cardFeb);

                const row = Math.floor(i / cardsPerRow);
                const col = i % cardsPerRow;

                const startX = -scrollViewWidth / 2 + paddingLeft + cardWidth / 2;
                const x = startX + col * (cardWidth + spacingX);
                const y = currentY - row * (cardHeight + spacingY) - cardHeight / 2;

                cardNode.setPosition(new Vec3(x, y, 0));
                const isCurrent = effects[unlockedCount + i].effectId === currentEffectId;
                cardComponent.init(effects[unlockedCount + i], true, effects[unlockedCount + i].rarity, isCurrent);
                contentNode.addChild(cardNode);
            }
        }
    }

    // 装备变化时刷新UI
    onEquipmentChanged(data: { type: string, id: number }) {
        console.log(`装备已更换: ${data.type} -> ${data.id}`);

        // 只有当前显示的特效类型发生变化时才刷新
        if ((data.type === 'launchEffect' && this.currentEffectType === 'launch') ||
            (data.type === 'collisionEffect' && this.currentEffectType === 'collision') ||
            (data.type === 'trailEffect' && this.currentEffectType === 'trail')) {

            // 重新渲染卡片以更新"当前使用"状态
            this.renderEffectCards();
        }
    }

    // 清理事件监听
    onDestroy() {
        director.off('equipmentChanged', this.onEquipmentChanged, this);
    }

    update(deltaTime: number) {

    }
}


