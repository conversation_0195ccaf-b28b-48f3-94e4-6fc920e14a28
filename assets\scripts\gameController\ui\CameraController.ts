import { _decorator, Component, Node, Camera, Vec3, UITransform } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('CameraController')
export class CameraController extends Component {
    @property(Node)
    targetToFollow: Node = null; // 当前需要跟随的目标

    @property
    smoothFactor: number = 0.1; // 平滑跟随因子

    //地板节点
    @property(Node)
    floorNode: Node = null;

    private camera: Camera = null;

    start() {
        this.camera = this.getComponent(Camera);
    }

    update() {
        // 如果有跟随目标，相机跟随目标移动
        if (this.targetToFollow && this.targetToFollow.active) {
            const targetPos = this.targetToFollow.position;
            
            // 计算目标位置，但不直接设置
            let newX = this.node.position.x + (targetPos.x - this.node.position.x) * this.smoothFactor;
            let newY = this.node.position.y + (targetPos.y - this.node.position.y) * this.smoothFactor;
            
            // 应用边界限制
            const clampedPosition = this.clampCameraPosition(newX, newY);
            
            // 设置相机位置
            this.node.setPosition(clampedPosition.x, clampedPosition.y, 0);
        }
    }

    // 限制相机位置，确保视图不超出地板边界
    private clampCameraPosition(x: number, y: number): Vec3 {
        // 查找Canvas节点
        const canvas = this.node.parent;
        const canvasSize = canvas.getComponent(UITransform).contentSize;
        
        // 计算宽高比
        const aspect = canvasSize.width / canvasSize.height;
        
        // 计算相机可视区域的一半宽度和高度
        const halfVisibleWidth = this.camera.orthoHeight * aspect;
        const halfVisibleHeight = this.camera.orthoHeight;
        
        // 计算地板的一半宽度和高度
        const halfBoundarySize = this.floorNode.getComponent(UITransform).contentSize.width / 2;
        
        // 限制相机位置，确保相机视图不超出地板边界
        const clampedX = Math.max(-halfBoundarySize + halfVisibleWidth, Math.min(halfBoundarySize - halfVisibleWidth, x));
        const clampedY = Math.max(-halfBoundarySize + halfVisibleHeight, Math.min(halfBoundarySize - halfVisibleHeight, y));
        
        return new Vec3(clampedX, clampedY, 0);
    }

    // 设置跟随目标
    public setTarget(target: Node) {
        this.targetToFollow = target;
    }

    // // 设置相机缩放级别
    // public setZoom(zoomLevel: number) {
    //     if (this.camera) {
    //         // 调整正交高度来实现缩放
    //         this.camera.orthoHeight = 960 * zoomLevel; // 基础高度 * 缩放系数
            
    //         // 缩放后重新应用边界限制
    //         const clampedPosition = this.clampCameraPosition(this.node.position.x, this.node.position.y);
    //         this.node.setPosition(clampedPosition.x, clampedPosition.y, this.node.position.z);
    //     }
    // }

    // 设置地板大小
    
}





