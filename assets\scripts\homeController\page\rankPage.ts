import { _decorator, Button, Component, Node, Prefab, ScrollView, resources, JsonAsset, instantiate, UITransform, Label, director } from 'cc';
import { provinceFab } from '../fab/provinceFab';
import { ranklist } from '../ranklist';
import { DataManager } from '../../Managers/DataManager';
import { loading } from '../loading';
const { ccclass, property } = _decorator;

@ccclass('rankPage')
export class rankPage extends Component {

    @property(Prefab)
    provinceRankPrefab: Prefab = null;

    @property(Button)
    rankButton: Button = null;

    @property(Button)
    postionButton: Button = null;

    @property(Button)
    introduceButton: Button = null;

    @property(Button)
    limitmarbleButton: Button = null;

    @property(Button)
    startButton: Button = null;

    @property(ScrollView)
    scrollView: ScrollView = null;

    @property(Node)
    introduceNode: Node = null;

    @property(Node)
    rankListNode: Node = null;

    @property(Node)
    turntableNode: Node = null;

    @property(Label)
    teamNameLabel: Label = null;

    @property(loading)
    loadingPage: loading = null;

    private rankData: any = null;
    private dataManager: DataManager = null;

    start() {
        this.dataManager = DataManager.getInstance(); // 获取数据管理器实例，用于获取用户信息
        this.loadProvinceRankData();
        this.introduceButton.node.on('click', this.showIntroduce, this);
        this.rankButton.node.on('click', this.showRankList, this);
        this.limitmarbleButton.node.on('click', this.showLimitMarble, this);
        this.postionButton.node.on('click', this.showPostion, this);
        this.startButton.node.on('click', this.startgame, this);
        this.teamNameLabel.string = `${this.dataManager.User.province}队`;
        director.on('provinceUpdated', this.onUpdateProvince, this);
    }

    startgame() {

        const dataManager = DataManager.getInstance();
        dataManager.setLevel('primary');

        this.loadingPage.showAndLoadGame(
            dataManager.User?.username,
            "AI对手",
            dataManager.User?.level,
            18
        );
    }

    onUpdateProvince() {
        this.teamNameLabel.string = `${this.dataManager.User.province}队`;
    }

    showPostion() {
        const postion = this.node.scene.getChildByName('Canvas').getChildByName('postion');
        postion.active = true;
    }
    showLimitMarble() {
        this.turntableNode.active = true;
    }

    showRankList() {
        this.rankListNode.active = true;
    }

    showIntroduce() {
        this.introduceNode.active = true;
        this, this.introduceNode.getChildByName('close').getComponent(Button).node.on('click', this.closeIntroduce, this);
    }

    closeIntroduce() {
        this.introduceNode.active = false;
    }

    // 加载省份排行榜数据
    loadProvinceRankData() {
        resources.load('data/ranklist', JsonAsset, (err, asset) => {
            if (!err) {
                this.rankData = asset.json;
                this.showProvinceRank();
            } else {
                console.error('加载排行榜数据失败:', err);
            }
        });
    }

    // 显示省份排行榜
    showProvinceRank() {
        if (!this.rankData || !this.provinceRankPrefab || !this.scrollView) return;

        // 清空滚动视图内容
        this.clearScrollContent();

        const regionData = this.rankData.regionRank;
        for (let i = 0; i < regionData.length; i++) {
            const item = regionData[i];
            const rankNode = instantiate(this.provinceRankPrefab);
            const rankComponent = rankNode.getComponent(provinceFab);

            if (rankComponent) {
                rankComponent.set(item.province, item.rank, item.rate);
                // console.log(item.province, item.rank, item.rate);
            }
            this.scrollView.content.addChild(rankNode);
        }

    }

    // 清空滚动视图内容
    clearScrollContent() {
        if (this.scrollView && this.scrollView.content) {
            this.scrollView.content.removeAllChildren();
        }
    }

    update(deltaTime: number) {

    }
}


