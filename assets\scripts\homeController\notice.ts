import { _decorator, Button, Component, Label, Node, JsonAsset, resources } from 'cc';
const { ccclass, property } = _decorator;

/*
    公告系统组件 - 管理游戏公告和通知信息展示

    主要功能：
    1. 公告展示：显示游戏更新公告和重要通知
    2. 内容管理：支持多条公告的标题和内容显示
    3. 数据加载：从配置文件加载公告内容
    4. 界面控制：提供关闭和滚动查看功能

    使用方式：
    - 通过BattlePage的公告按钮激活显示
    - 自动加载公告配置数据
    - 支持多条公告的切换和查看
*/

@ccclass('notice')
export class notice extends Component {

    @property(Label)
    title1Label: Label = null;

    // 更新公告
    @property(Label)
    noticeLabel1: Label = null;

    @property(Label)
    title2Label: Label = null;

    // 平台运营声明
    @property(Label)
    noticeLabel2: Label = null;

    @property(Button)
    closeButton: Button = null;

    start() {
        this.loadNotice();
        this.closeButton.node.on('click', this.closeNotice, this);
    }

    loadNotice() {
        // 获取公告数据
        resources.load("data/notices", JsonAsset, (err, asset) => {
            if (err) {
                console.error("加载公告数据失败:", err);
                return;
            }

            const notices = asset.json.notices || [];
            // console.log(`成功加载${notices.length}条公告`);

            // 显示公告内容
            this.displayNotices(notices);
        });
    }

    displayNotices(notices: any[]) {

        // 显示第一条公告
        const firstNotice = notices[0];
        if (this.title1Label) {
            this.title1Label.string = firstNotice.title;
        }
        if (this.noticeLabel1) {
            this.noticeLabel1.string = firstNotice.content;
        }

        const secondNotice = notices[1];
        if (this.title2Label) {
            this.title2Label.string = secondNotice.title;
        }
        if (this.noticeLabel2) {
            this.noticeLabel2.string = secondNotice.content;
        }

        // // 统计未读公告数量
        // const unreadCount = notices.filter(notice => !notice.isRead).length;
        // console.log(`有${unreadCount}条未读公告`);
    }

    closeNotice() {
        this.node.active = false;
    }

    update(deltaTime: number) {

    }


}


