import { _decorator, Component, Label, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('result')
export class result extends Component {
    start() {

    }
    set(name: string, count: number) {
        this.node.getChildByName("name").getComponent(Label).string = name;
        this.node.getChildByName("count").getComponent(Label).string = count.toString();
    }
    update() {

    }
}


