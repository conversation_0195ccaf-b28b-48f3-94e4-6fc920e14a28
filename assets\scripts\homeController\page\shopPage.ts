import { _decorator, Component, Node, Prefab, resources, JsonAsset, instantiate, ProgressBar, Label, Button, Sprite, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, director, sp } from 'cc';
import { cardFeb } from '../fab/cardFab';
import { DataManager } from '../../Managers/DataManager';
import { User } from '../../Entity/UserEntity';
import { resultTip } from '../fab/resultTip';
const { ccclass, property } = _decorator;

/*
    提供刷新随机4种皮肤，4种特效并展示
*/


@ccclass('shopPage')
export class shopPage extends Component {

    // 卡片预制体
    @property(Prefab)
    card: Prefab = null;

    @property(Node)
    list: Node = null;

    @property(Node)
    skinTreasure: Node = null;

    @property(Node)
    effectTreasure: Node = null;

    @property(Label)
    restCount: Label = null;

    @property(Button)
    changeBtn1: Button = null;

    @property(Button)
    changeBtn2: Button = null;

    @property(Label)
    restTimeLabel: Label = null;

    @property(Button)
    freshBtn: Button = null;

    @property(Label)
    restCountLabel: Label = null;

    @property(Prefab)
    resultTipPrefab: Prefab = null;

    @property(sp.Skeleton)
    skeleton1: sp.Skeleton = null;

    @property(sp.Skeleton)
    skeleton2: sp.Skeleton = null;

    private skinsData: any[] = [];
    private effectsData: any[] = [];
    private allSkinsData: any[] = [];
    private allEffectsData: any[] = [];

    private dataManager: DataManager = null;
    private currentUser: User = null;

    private skinDrawMode: number = 1;
    private effectDrawMode: number = 1;

    onload() {
        console.log('shopPage onload');
    }
    start() {
        console.log('shopPage start');
        this.dataManager = DataManager.getInstance();
        this.currentUser = this.dataManager.User;
        this.loadRandomSkin_Effects();
        this.updateRestCount();
        this.updateRestTime();

        // 观看视频，花费10颗弹珠获取抽奖机会来抽奖
        this.skinTreasure.getChildByName('byvideo').on('click', this.byvideoSkin, this);
        this.skinTreasure.getChildByName('bymarble').on('click', this.bymarbleSkin, this);
        this.effectTreasure.getChildByName('byvideo').on('click', this.byvideoEffect, this);
        this.effectTreasure.getChildByName('bymarble').on('click', this.bymarbleEffect, this);
        this.changeBtn1.node.on('click', this.changeBtn1Func, this);
        this.changeBtn2.node.on('click', this.changeBtn2Func, this);
        this.freshBtn.node.on('click', this.freshBtnFunc, this);
    }

    freshBtnFunc() {
        // 刷新随机4种皮肤，4种特效并展示
        this.loadRandomSkin_Effects();
    }

    changeBtn1Func() {
        if (this.skinDrawMode == 1) {
            this.skinDrawMode = 10;
            resources.load('homeUI/change10/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                this.changeBtn1.getComponent(Sprite).spriteFrame = spriteFrame;
            })
        }
        else {
            this.skinDrawMode = 1;
            resources.load('homeUI/change1/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                this.changeBtn1.getComponent(Sprite).spriteFrame = spriteFrame;
            })
        }

    }

    changeBtn2Func() {
        if (this.effectDrawMode == 1) {
            this.effectDrawMode = 10;
            resources.load('homeUI/change10/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                this.changeBtn2.getComponent(Sprite).spriteFrame = spriteFrame;
            })
        }
        else {
            this.effectDrawMode = 1;
            resources.load('homeUI/change1/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                this.changeBtn2.getComponent(Sprite).spriteFrame = spriteFrame;
            })
        }
    }

    async loadRandomSkin_Effects() {
        console.log("加载随机皮肤和特效");
        try {
            // 并行加载四个数据文件
            const [skinsAsset, launchEffectsAsset, collisionEffectsAsset, trailEffectsAsset] = await Promise.all([
                this.loadJsonAsset('data/skins'),
                this.loadJsonAsset('data/launchEffects'),
                this.loadJsonAsset('data/collisionEffects'),
                this.loadJsonAsset('data/trailEffects')
            ]);

            // 保存所有数据用于抽取
            this.allSkinsData = (skinsAsset as JsonAsset).json.skins;
            const allLaunchEffects = (launchEffectsAsset as JsonAsset).json.launchEffects;
            const allCollisionEffects = (collisionEffectsAsset as JsonAsset).json.collisionEffects;
            const allTrailEffects = (trailEffectsAsset as JsonAsset).json.trailEffects;

            // 合并所有特效数据
            this.allEffectsData = [...allLaunchEffects, ...allCollisionEffects, ...allTrailEffects];

            // 随机选择4个用于展示
            this.skinsData = this.getRandomItems(this.allSkinsData, 4);
            this.effectsData = this.getRandomItems(this.allEffectsData, 4);

            this.createSkinCards();

        } catch (error) {
            console.error("加载随机数据失败:", error);
        }
    }

    createSkinCards() {
        for (let i = 0; i < 4; i++) {
            const skin = this.skinsData[i];
            const cardNode = instantiate(this.card);

            // 设置卡片节点的属性
            const cardComponent = cardNode.getComponent(cardFeb);
            const isUnlocked = this.currentUser.hasSkin(skin.skinId);
            cardComponent.init1(skin, isUnlocked);

            // 创建卡片节点
            this.list.addChild(cardNode); // 添加到节点树中，显示卡片节点
        }
        for (let i = 0; i < 4; i++) {
            const effect = this.effectsData[i];
            const cardNode = instantiate(this.card);

            // 设置卡片节点的属性
            const cardComponent = cardNode.getComponent(cardFeb);
            // 根据特效类型判断是否解锁
            let isUnlocked = false;
            if (effect.launchEffectId) {
                isUnlocked = this.currentUser.hasLaunchEffect(effect.launchEffectId);
            } else if (effect.collisionEffectId) {
                isUnlocked = this.currentUser.hasCollisionEffect(effect.collisionEffectId);
            } else if (effect.trailEffectId) {
                isUnlocked = this.currentUser.hasTrailEffect(effect.trailEffectId);
            }
            cardComponent.init1(effect, isUnlocked);

            // 创建卡片节点
            this.list.addChild(cardNode); // 添加到节点树中，显示卡片节点
        }
    }

    loadJsonAsset(path) {
        return new Promise((resolve, reject) => {
            resources.load(path, JsonAsset, (err, asset) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(asset);
                }
            });
        });
    }

    getRandomItems(array, count) {
        const shuffled = [...array].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, Math.min(count, array.length));
    }


    // 保存抽取次数（现在保存到用户数据）
    saveDrawCounts() {
        this.currentUser.save();
    }

    // 视频抽取皮肤
    byvideoSkin() {
        console.log("观看视频抽取皮肤");
        this.drawSkin(true);
    }

    // 弹珠抽取皮肤
    bymarbleSkin() {
        if (this.skinDrawMode == 1) {
            // 1抽：花费10弹珠
            if (!this.currentUser.deductMarbles(10)) return;
            console.log("花费10弹珠抽取皮肤");
            this.drawSkin(false);
            this.updateTopBarMarbleCount();
        }
        else {
            // 10连抽：花费90弹珠（优惠10弹珠）
            if (!this.currentUser.deductMarbles(90)) return;
            console.log("花费90弹珠进行10连抽皮肤");
            this.drawSkinTenTimes(false);
            this.updateTopBarMarbleCount();
        }
    }

    // 视频抽取特效
    byvideoEffect() {
        console.log("观看视频抽取特效");
        this.drawEffect(true);
    }

    // 弹珠抽取特效
    bymarbleEffect() {
        if (this.effectDrawMode == 1) {
            // 1抽：花费10弹珠
            if (!this.currentUser.deductMarbles(10)) return;
            console.log("花费10弹珠抽取特效");
            this.drawEffect(false);
            this.updateTopBarMarbleCount();
        }
        else {
            // 10连抽：花费90弹珠（优惠10弹珠）
            if (!this.currentUser.deductMarbles(90)) return;
            console.log("花费90弹珠进行10连抽特效");
            this.drawEffectTenTimes(false);
            this.updateTopBarMarbleCount();
        }
    }

    // 播放开箱动画
    playOpenBoxAnimation(callback: () => void) {
        // 显示动画节点
        if (this.skeleton1) {
            this.skeleton1.node.active = true;
            // 播放第一个开箱动画
            this.skeleton1.setAnimation(0, 'animation', false);

            // 监听第一个动画完成
            this.skeleton1.setCompleteListener(() => {
                // 第一个动画完成后，播放第二个动画
                if (this.skeleton2) {
                    this.skeleton2.node.active = true;
                    this.skeleton2.setAnimation(0, 'animation', false);

                    // 监听第二个动画完成
                    this.skeleton2.setCompleteListener(() => {
                        // 两个动画都完成后，隐藏动画节点并执行回调
                        this.skeleton1.node.active = false;
                        this.skeleton2.node.active = false;

                        // 清除监听器
                        this.skeleton1.setCompleteListener(null);
                        this.skeleton2.setCompleteListener(null);

                        // 执行回调显示结果
                        if (callback) {
                            callback();
                        }
                    });
                } else {
                    // 如果没有第二个动画，直接执行回调
                    this.skeleton1.node.active = false;
                    this.skeleton1.setCompleteListener(null);
                    if (callback) {
                        callback();
                    }
                }
            });
        } else {
            // 如果没有动画组件，直接执行回调
            if (callback) {
                callback();
            }
        }
    }

    // 抽取皮肤
    drawSkin(isVideo: boolean) {
        this.currentUser.sharedDrawCount++;
        const item = this.drawItem(this.allSkinsData, this.currentUser.sharedDrawCount, 'skin');
        this.handleDrawResult(item, 'skin');
        this.saveDrawCounts();
        this.updateRestCount();

        // 播放开箱动画，然后显示结果
        this.playOpenBoxAnimation(() => {
            this.showSingleDrawResult(item, 'skin');
        });
    }

    // 抽取特效
    drawEffect(isVideo: boolean) {
        this.currentUser.sharedDrawCount++;
        const item = this.drawItem(this.allEffectsData, this.currentUser.sharedDrawCount, 'effect');
        this.handleDrawResult(item, 'effect');
        this.saveDrawCounts();
        this.updateRestCount();

        // 播放开箱动画，然后显示结果
        this.playOpenBoxAnimation(() => {
            this.showSingleDrawResult(item, 'effect');
        });
    }

    // 10连抽皮肤
    drawSkinTenTimes(isVideo: boolean) {
        const results: any[] = [];

        // 进行10次抽取
        for (let i = 0; i < 10; i++) {
            this.currentUser.sharedDrawCount++;
            const item = this.drawItem(this.allSkinsData, this.currentUser.sharedDrawCount, 'skin');
            results.push(item);
            this.handleDrawResult(item, 'skin');
        }

        this.saveDrawCounts();
        this.updateRestCount();

        // 显示10连抽结果
        this.showTenDrawResults(results, 'skin');
    }

    // 10连抽特效
    drawEffectTenTimes(isVideo: boolean) {
        const results: any[] = [];

        // 进行10次抽取
        for (let i = 0; i < 10; i++) {
            this.currentUser.sharedDrawCount++;
            const item = this.drawItem(this.allEffectsData, this.currentUser.sharedDrawCount, 'effect');
            results.push(item);
            this.handleDrawResult(item, 'effect');
        }

        this.saveDrawCounts();
        this.updateRestCount();

        // 显示10连抽结果
        this.showTenDrawResults(results, 'effect');
    }

    // 抽取物品核心逻辑 - 皮肤和特效共享保底
    drawItem(dataArray: any[], drawCount: number, type: string) {
        // 70抽必出五星保底（根据抽取类型出对应的五星）
        if (drawCount >= 70) {
            const fiveStarItems = dataArray.filter(item => item.rarity === 5);
            this.resetDrawCount();
            return fiveStarItems[Math.floor(Math.random() * fiveStarItems.length)];
        }

        // 正常概率抽取
        const rand = Math.random();
        let rarity: number;

        if (rand < 0.01) rarity = 5;      // 1% 五星
        else if (rand < 0.06) rarity = 4; // 5% 四星
        else rarity = 3;                  // 94% 三星

        const items = dataArray.filter(item => item.rarity === rarity);
        return items[Math.floor(Math.random() * items.length)];
    }

    // 处理抽取结果
    handleDrawResult(item: any, type: string) {
        let itemId: number;
        let itemName: string;
        let isOwned = false;

        if (type === 'skin') {
            itemId = item.skinId;
            itemName = item.skinName;
            isOwned = this.currentUser.ownedSkins.includes(itemId);
        } else {
            itemName = item.effectName;
            if (item.launchEffectId) {
                itemId = item.launchEffectId;
                isOwned = this.currentUser.ownedLaunchEffects.includes(itemId);
            } else if (item.collisionEffectId) {
                itemId = item.collisionEffectId;
                isOwned = this.currentUser.ownedCollisionEffects.includes(itemId);
            } else if (item.trailEffectId) {
                itemId = item.trailEffectId;
                isOwned = this.currentUser.ownedTrailEffects.includes(itemId);
            }
        }

        if (isOwned) {
            // 转换为碎片
            const pieces = item.rarity === 4 ? 10 : item.rarity === 5 ? 20 : 0;
            if (pieces > 0) {
                this.currentUser.addPieces(pieces);
                console.log(`获得${itemName}(已拥有) -> ${pieces}碎片`);
            } else {
                console.log(`获得${itemName}(已拥有) -> 无碎片`);
            }
        } else {
            // 添加到拥有列表
            if (type === 'skin') {
                this.currentUser.ownedSkins.push(itemId);
            } else if (item.launchEffectId) {
                this.currentUser.ownedLaunchEffects.push(itemId);
            } else if (item.collisionEffectId) {
                this.currentUser.ownedCollisionEffects.push(itemId);
            } else if (item.trailEffectId) {
                this.currentUser.ownedTrailEffects.push(itemId);
            }
            console.log(`获得新${type === 'skin' ? '皮肤' : '特效'}: ${itemName}`);
        }

        if (item.rarity === 5) {
            this.resetDrawCount();
        }
    }

    // 重置抽取计数
    resetDrawCount() {
        this.currentUser.sharedDrawCount = 0;
        console.log(`共享抽取计数已重置`);
    }

    // 更新剩余保底次数显示
    updateRestCount() {
        if (this.restCount) {
            const remaining = 70 - this.currentUser.sharedDrawCount;
            console.log(`剩余保底次数: ${remaining}`);
            this.restCount.string = remaining.toString();
        }
    }

    // 更新剩余时间显示（到明天0点的时间）
    updateRestTime() {
        if (this.restTimeLabel) {
            const now = new Date();
            const tomorrow = new Date(now);
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(0, 0, 0, 0);

            const timeDiff = tomorrow.getTime() - now.getTime();

            const hours = Math.floor(timeDiff / (1000 * 60 * 60));
            const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

            this.restTimeLabel.string = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    // 显示单抽结果
    showSingleDrawResult(item: any, type: string) {
        if (!this.resultTipPrefab) return;

        let itemName: string;
        let itemId: number;
        let isOwned = false;

        if (type === 'skin') {
            itemName = item.skinName;
            itemId = item.skinId;
            isOwned = this.currentUser.ownedSkins.includes(itemId);
        } else {
            itemName = item.effectName;
            if (item.launchEffectId) {
                itemId = item.launchEffectId;
                isOwned = this.currentUser.ownedLaunchEffects.includes(itemId);
            } else if (item.collisionEffectId) {
                itemId = item.collisionEffectId;
                isOwned = this.currentUser.ownedCollisionEffects.includes(itemId);
            } else if (item.trailEffectId) {
                itemId = item.trailEffectId;
                isOwned = this.currentUser.ownedTrailEffects.includes(itemId);
            }
        }

        let rewards: Array<{name: string, count: number}> = [];

        if (isOwned) {
            // 已拥有，显示获得的碎片
            const pieces = item.rarity === 4 ? 10 : item.rarity === 5 ? 20 : 0;
            if (pieces > 0) {
                rewards.push({name: "普通碎片", count: pieces});
            }
        } else {
            // 新获得的物品
            rewards.push({name: itemName, count: 1});
        }

        // 创建并显示结果弹窗
        if (rewards.length > 0) {
            const resultTipNode = instantiate(this.resultTipPrefab);
            const resultTipComponent = resultTipNode.getComponent(resultTip);
            if (resultTipComponent) {
                resultTipComponent.set(rewards);
            }

            // 添加到场景根节点，确保在最上层显示
            this.node.scene.getChildByName('Canvas').addChild(resultTipNode);
        }
    }

    // 显示10连抽结果
    showTenDrawResults(results: any[], type: string) {
        if (!this.resultTipPrefab) return;

        // 将抽取结果转换为显示格式
        const rewards: Array<{name: string, count: number}> = [];
        const itemCounts = new Map<string, number>();

        // 统计每种物品的数量
        results.forEach(item => {
            const itemName = type === 'skin' ? item.skinName : item.effectName;
            const currentCount = itemCounts.get(itemName) || 0;
            itemCounts.set(itemName, currentCount + 1);
        });

        // 转换为显示格式
        itemCounts.forEach((count, name) => {
            rewards.push({name: name, count: count});
        });

        // 创建并显示结果弹窗
        const resultTipNode = instantiate(this.resultTipPrefab);
        const resultTipComponent = resultTipNode.getComponent(resultTip);
        if (resultTipComponent) {
            resultTipComponent.set(rewards);
        }

        // 添加到场景根节点，确保在最上层显示
        this.node.scene.getChildByName('Canvas').addChild(resultTipNode);
    }

    /**
     * 更新TopBar的弹珠数量显示
     */
    private updateTopBarMarbleCount() {
        director.emit('updateTopBarMarbleCount');
    }

    update(deltaTime: number) {
        // 每帧更新剩余时间显示
        this.updateRestTime();
    }
}


