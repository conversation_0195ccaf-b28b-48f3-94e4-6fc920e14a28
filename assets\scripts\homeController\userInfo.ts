import { _decorator, Button, Component, Label, Node } from 'cc';
const { ccclass, property } = _decorator;

/*
    用户信息组件 - 管理用户个人信息展示和编辑

    主要功能：
    1. 信息展示：显示用户ID和所属省份信息
    2. 信息编辑：支持保存和修改用户信息
    3. 省份选择：提供省份选择功能入口
    4. 界面控制：提供关闭和确认操作

    使用方式：
    - 通过show()方法传入用户ID和省份信息激活显示
    - 支持保存、选择省份和关闭操作
    - 与postion组件配合实现省份选择功能
*/

@ccclass('userInfo')
export class userInfo extends Component {
    start() {
        this.node.getChildByName("save").on('click', this.save, this);
        this.node.getChildByName("choose").on('click', this.choose, this);
        this.node.getChildByName("close").on('click', this.close, this);
    }
    set(id: number, province: string, name: string) {
        
        this.node.getChildByName("id").getComponent(Label).string = id.toString();
        this.node.getChildByName("province").getComponent(Label).string = province;
        this.node.getChildByName("name").getComponent(Label).string = name;
    }
    save() {
        console.log("save");
        this.node.active = false;
    }
    choose() {
        const postion = this.node.scene.getChildByName('Canvas').getChildByName('postion');
        postion.active = true;
    }
    close() {
        this.node.active = false;
    }
    update(deltaTime: number) {
        // console.log(this.node.active);
    }
}


