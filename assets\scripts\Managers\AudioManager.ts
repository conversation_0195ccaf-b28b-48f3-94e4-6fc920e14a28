import { _decorator, Component, AudioClip, AudioSource } from 'cc';
const { ccclass, property } = _decorator;

/*
    管理游戏音效和背景音乐
*/

@ccclass('AudioManager')
export class AudioManager extends Component {
    private static _instance: AudioManager = null;
    
    @property(AudioSource)
    private musicSource: AudioSource = null;
    
    @property(AudioSource)
    private sfxSource: AudioSource = null;
    
    @property(AudioClip)
    private bgMusic: AudioClip = null;
    
    @property([AudioClip])
    private shootSounds: AudioClip[] = [];
    
    @property(AudioClip)
    private hitSound: AudioClip = null;
    
    @property(AudioClip)
    private winSound: AudioClip = null;
    
    @property(AudioClip)
    private loseSound: AudioClip = null;
    
    private _isMuted: boolean = false;

    // 单例模式获取实例
    public static getInstance(): AudioManager {
        return this._instance;
    }

    onLoad() {
       if (AudioManager._instance !== null) {
           console.error("AudioManager already exists!");
           return;
       }
       AudioManager._instance = this;
    }
    
    start() {
        // 播放背景音乐
        this.playMusic();
    }
    
    // 播放背景音乐
    public playMusic() {
        if (this.musicSource && this.bgMusic && !this._isMuted) {
            this.musicSource.clip = this.bgMusic;
            this.musicSource.loop = true;
            this.musicSource.play();
        }
    }
    
    // 停止背景音乐
    public stopMusic() {
        if (this.musicSource) {
            this.musicSource.stop();
        }
    }
    
    // 播放发射音效
    public playShootSound() {
        if (this.sfxSource && this.shootSounds.length > 0 && !this._isMuted) {
            // 随机选择一个发射音效
            const randomIndex = Math.floor(Math.random() * this.shootSounds.length);
            this.sfxSource.playOneShot(this.shootSounds[randomIndex]);
        }
    }
    
    // 播放碰撞音效
    public playHitSound() {
        if (this.sfxSource && this.hitSound && !this._isMuted) {
            this.sfxSource.playOneShot(this.hitSound);
        }
    }
    
    // 播放胜利音效
    public playWinSound() {
        if (this.sfxSource && this.winSound && !this._isMuted) {
            this.sfxSource.playOneShot(this.winSound);
        }
    }
    
    // 播放失败音效
    public playLoseSound() {
        if (this.sfxSource && this.loseSound && !this._isMuted) {
            this.sfxSource.playOneShot(this.loseSound);
        }
    }
    
    // 设置静音
    public setMute(mute: boolean) {
        this._isMuted = mute;
        
        if (this.musicSource) {
            if (mute) {
                this.musicSource.pause();
            } else {
                this.musicSource.play();
            }
        }
    }
    
    // 设置音量
    public setVolume(volume: number) {
        if (this.musicSource) {
            this.musicSource.volume = volume;
        }
        
        if (this.sfxSource) {
            this.sfxSource.volume = volume;
        }
    }
}