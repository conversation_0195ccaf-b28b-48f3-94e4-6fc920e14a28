import { _decorator, Component, Node, resources, JsonAsset, instantiate, ScrollView, UITransform, Prefab, Layout, Vec3, Sprite, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, director } from 'cc';
import { cardFeb } from '../fab/cardFab';
import { DataManager } from '../../Managers/DataManager';
const { ccclass, property } = _decorator;

/*
    由数据渲染不同皮肤展示卡片,一行四个
*/

@ccclass('skinPage')
export class skinPage extends Component {

    // 皮肤的滚动视图
    @property(ScrollView)
    ScrollView: ScrollView = null;

    // 卡片预制体
    @property(Prefab)
    card: Prefab = null;

    private skinsData: any[] = [];
    private dataManager: DataManager = null;

    start() {
        this.dataManager = DataManager.getInstance();
        this.loadSkinsData();

        // 监听装备变化事件，刷新UI显示
        director.on('equipmentChanged', this.onEquipmentChanged, this);
    }

    loadSkinsData() {
        resources.load('data/skins', JsonAsset, (err, asset) => {
            if (err) {
                console.error("加载皮肤数据失败:", err);
                return;
            }

            this.skinsData = asset.json.skins;
            this.renderSkinCards();
        });
    }

    renderSkinCards() {
        if (!this.dataManager.User) console.error("用户数据未初始化");

        const ownedSkins = this.dataManager.User.ownedSkins;
        const unlockedSkins = [];
        const lockedSkins = [];

        // 分类皮肤
        this.skinsData.forEach(skin => {
            if (ownedSkins.indexOf(skin.skinId) !== -1) {
                unlockedSkins.push(skin);
            } else {
                lockedSkins.push(skin);
            }
        });

        // 合并皮肤数组，已解锁在前
        const allSkins = [...unlockedSkins, ...lockedSkins];

        // 在同一个滚动视图中渲染所有皮肤
        this.createSkinCards(allSkins, this.ScrollView, unlockedSkins.length);
        console.log("unlockedSkins: ", unlockedSkins);
    }

    createSkinCards(skins: any[], scrollView: ScrollView, unlockedCount: number) {
        const contentNode = scrollView.content;

        // 清空现有卡片
        contentNode.removeAllChildren();

        const cardsPerRow = 4;
        const cardWidth = 247;
        const cardHeight = 324;
        const spacingX = 10;
        const spacingY = 20;
        const paddingTop = 20;
        const paddingLeft = 40;
        const titleHeight = 70; // 标题行高度

        // 计算已解锁和未解锁的行数
        const unlockedRows = Math.ceil(unlockedCount / cardsPerRow);
        const lockedRows = Math.ceil((skins.length - unlockedCount) / cardsPerRow);

        // 获取ScrollView的宽度，使content宽度与ScrollView一致
        const scrollViewWidth = scrollView.node.getComponent(UITransform).contentSize.width;

        // 计算实际需要的内容高度
        let contentHeight = paddingTop * 2; // 上下padding

        // 如果有已解锁皮肤，添加标题和卡片高度
        if (unlockedCount > 0) {
            contentHeight += titleHeight + spacingY; // 已解锁标题
            contentHeight += unlockedRows * cardHeight + Math.max(0, unlockedRows - 1) * spacingY; // 已解锁卡片
            if (skins.length > unlockedCount) {
                contentHeight += spacingY; // 标题间距（只有在有未解锁皮肤时才添加）
            }
        }

        // 如果有未解锁皮肤，添加标题和卡片高度
        if (skins.length > unlockedCount) {
            contentHeight += titleHeight + spacingY; // 未解锁标题
            contentHeight += lockedRows * cardHeight + Math.max(0, lockedRows - 1) * spacingY; // 未解锁卡片
        }

        // 设置content大小和锚点
        const uiTransform = contentNode.getComponent(UITransform);
        uiTransform.setContentSize(scrollViewWidth, contentHeight);
        uiTransform.setAnchorPoint(0.5, 1);

        // // 调试信息
        // console.log(`皮肤页面 - 内容高度: ${contentHeight}, 滚动视图高度: ${scrollView.node.getComponent(UITransform).contentSize.height}`);
        // console.log(`已解锁皮肤数量: ${unlockedCount}, 总皮肤数量: ${skins.length}`);

        let currentY = -paddingTop;

        // 创建已解锁标题
        if (unlockedCount > 0) {
            const unlockedTitle = new Node("UnlockedTitle");
            const titleSprite = unlockedTitle.addComponent(Sprite);

            // 加载已解锁标题的sprite图片
            resources.load('homeUI/已解锁/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    titleSprite.spriteFrame = spriteFrame;
                }
            });

            unlockedTitle.setPosition(0, currentY - titleHeight / 2);
            contentNode.addChild(unlockedTitle);
            currentY -= titleHeight + spacingY;

            // 创建已解锁卡片
            for (let i = 0; i < unlockedCount; i++) {
                const cardNode = instantiate(this.card);
                const cardComponent = cardNode.getComponent(cardFeb);

                const row = Math.floor(i / cardsPerRow);
                const col = i % cardsPerRow;

                const startX = -scrollViewWidth / 2 + paddingLeft + cardWidth / 2;
                const x = startX + col * (cardWidth + spacingX);
                const y = currentY - row * (cardHeight + spacingY) - cardHeight / 2;

                cardNode.setPosition(new Vec3(x, y, 0));
                const currentSkinId = this.dataManager.User.currentSkin;
                const isCurrent = skins[i].skinId === currentSkinId;
                cardComponent.init(skins[i], false, skins[i].rarity, isCurrent);
                contentNode.addChild(cardNode);
            }

            // 更新Y位置到已解锁区域后
            const unlockedRows = Math.ceil(unlockedCount / cardsPerRow);
            currentY -= unlockedRows * (cardHeight + spacingY);
        }

        // 创建未解锁标题
        const lockedCount = skins.length - unlockedCount;
        if (lockedCount > 0) {
            const lockedTitle = new Node("LockedTitle");
            const titleSprite = lockedTitle.addComponent(Sprite);

            // 加载未解锁标题的sprite图片
            resources.load('homeUI/未解锁/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    titleSprite.spriteFrame = spriteFrame;
                }
            });

            lockedTitle.setPosition(0, currentY - titleHeight / 2);
            contentNode.addChild(lockedTitle);
            currentY -= titleHeight + spacingY;

            // 创建未解锁卡片
            for (let i = 0; i < lockedCount; i++) {
                const cardNode = instantiate(this.card);
                const cardComponent = cardNode.getComponent(cardFeb);

                const row = Math.floor(i / cardsPerRow);
                const col = i % cardsPerRow;

                const startX = -scrollViewWidth / 2 + paddingLeft + cardWidth / 2;
                const x = startX + col * (cardWidth + spacingX);
                const y = currentY - row * (cardHeight + spacingY) - cardHeight / 2;

                cardNode.setPosition(new Vec3(x, y, 0));
                const currentSkinId = this.dataManager.User.currentSkin;
                const isCurrent = skins[unlockedCount + i].skinId === currentSkinId;
                cardComponent.init(skins[unlockedCount + i], true, skins[unlockedCount + i].rarity, isCurrent);
                contentNode.addChild(cardNode);
            }
        }
    }



    // 装备变化时刷新UI
    onEquipmentChanged(data: { type: string, id: number }) {
        console.log(`装备已更换: ${data.type} -> ${data.id}`);

        // 只有皮肤变化时才刷新
        if (data.type === 'skin') {
            // 重新渲染卡片以更新"当前使用"状态
            this.renderSkinCards();
        }
    }

    // 清理事件监听
    onDestroy() {
        director.off('equipmentChanged', this.onEquipmentChanged, this);
    }

    update(deltaTime: number) {

    }
}


