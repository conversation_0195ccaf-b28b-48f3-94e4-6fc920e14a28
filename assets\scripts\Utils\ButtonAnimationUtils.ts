import { Node, tween, Vec3 } from 'cc';

/**
 * 按钮动画工具类 - 非组件类的静态工具
 * 提供统一的按钮动画效果，避免每个按钮都挂载组件
 */
export class ButtonAnimationUtils {
    
    // 默认动画配置
    private static readonly DEFAULT_CONFIG = {
        scaleRatio: 0.9,
        animationDuration: 0.1,
        easing: 'backOut'
    };

    // 正在动画的按钮集合
    private static animatingButtons: Set<Node> = new Set();

    // 保存按钮的原始缩放值
    private static originalScales: Map<Node, Vec3> = new Map();

    /**
     * 播放点击动画（完整的按下-松开动画）
     * @param button 按钮节点
     * @param config 动画配置（可选）
     */
    static playClickAnimation(button: Node, config?: Partial<typeof ButtonAnimationUtils.DEFAULT_CONFIG>) {
        if (!button || !button.isValid) {
            return;
        }

        // 停止当前动画
        tween(button).stop();
        this.animatingButtons.delete(button);

        const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
        // 获取保存的原始缩放值，如果没有则使用当前缩放值
        const originalScale = this.originalScales.get(button) || button.scale.clone();

        this.animatingButtons.add(button);

        const scaleDown = originalScale.clone().multiplyScalar(finalConfig.scaleRatio);

        tween(button)
            .to(finalConfig.animationDuration / 2, { scale: scaleDown }, { easing: 'quadOut' as any })
            .to(finalConfig.animationDuration / 2, { scale: originalScale }, { easing: finalConfig.easing as any })
            .call(() => {
                this.animatingButtons.delete(button);
            })
            .start();
    }



    /**
     * 播放按压动画（按下时缩小）
     * @param button 按钮节点
     * @param config 动画配置（可选）
     */
    static playPressAnimation(button: Node, config?: Partial<typeof ButtonAnimationUtils.DEFAULT_CONFIG>) {
        if (!button || !button.isValid || this.animatingButtons.has(button)) {
            return;
        }

        const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
        const originalScale = button.scale.clone();
        const targetScale = originalScale.clone().multiplyScalar(finalConfig.scaleRatio);
        
        this.animatingButtons.add(button);
        
        tween(button)
            .to(finalConfig.animationDuration, { scale: targetScale }, { easing: 'quadOut' })
            .call(() => {
                this.animatingButtons.delete(button);
            })
            .start();
    }

    /**
     * 播放释放动画（松开时恢复）
     * @param button 按钮节点
     * @param config 动画配置（可选）
     */
    static playReleaseAnimation(button: Node, config?: Partial<typeof ButtonAnimationUtils.DEFAULT_CONFIG>) {
        if (!button || !button.isValid) {
            return;
        }

        // 停止当前动画
        tween(button).stop();
        this.animatingButtons.delete(button);

        const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
        // 获取保存的原始缩放值，如果没有则使用Vec3.ONE
        const originalScale = this.originalScales.get(button) || Vec3.ONE.clone();

        this.animatingButtons.add(button);

        tween(button)
            .to(finalConfig.animationDuration, { scale: originalScale }, { easing: finalConfig.easing as any })
            .call(() => {
                this.animatingButtons.delete(button);
            })
            .start();
    }

    /**
     * 停止按钮动画并重置状态
     * @param button 按钮节点
     */
    static stopAnimation(button: Node) {
        if (!button || !button.isValid) {
            return;
        }

        tween(button).stop();
        this.animatingButtons.delete(button);
        
        // 重置到原始缩放
        button.scale = Vec3.ONE.clone();
    }

    /**
     * 检查按钮是否正在动画
     * @param button 按钮节点
     * @returns 是否正在动画
     */
    static isAnimating(button: Node): boolean {
        return this.animatingButtons.has(button);
    }

    /**
     * 清理所有动画状态（场景切换时调用）
     */
    static clearAllAnimations() {
        this.animatingButtons.forEach(button => {
            if (button && button.isValid) {
                tween(button).stop();
            }
        });
        this.animatingButtons.clear();
        this.originalScales.clear();
    }

    /**
     * 为按钮设置完整的交互动画（移动端优化版本）
     * @param button 按钮节点
     * @param config 动画配置（可选）
     */
    static setupButtonInteraction(button: Node, config?: Partial<typeof ButtonAnimationUtils.DEFAULT_CONFIG>) {
        if (!button || !button.isValid) {
            return;
        }

        // 保存原始缩放值
        this.originalScales.set(button, button.scale.clone());

        let isPressed = false;

        // 添加安全机制：定时检查确保回弹
        let timeoutId: any = null;

        const ensureRelease = () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
            timeoutId = setTimeout(() => {
                if (isPressed) {
                    isPressed = false;
                    this.playReleaseAnimation(button, config);
                }
            }, 300); // 300ms后强制回弹
        };

        // 绑定触摸事件（移动端优化）
        button.on(Node.EventType.TOUCH_START, () => {
            isPressed = true;
            this.playPressAnimation(button, config);
            ensureRelease(); // 启动安全机制
        });

        button.on(Node.EventType.TOUCH_END, () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
                timeoutId = null;
            }
            if (isPressed) {
                isPressed = false;
                this.playReleaseAnimation(button, config);
            }
        });

        button.on(Node.EventType.TOUCH_CANCEL, () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
                timeoutId = null;
            }
            if (isPressed) {
                isPressed = false;
                this.playReleaseAnimation(button, config);
            }
        });


    }

    /**
     * 移除按钮的交互动画
     * @param button 按钮节点
     */
    static removeButtonInteraction(button: Node) {
        if (!button || !button.isValid) {
            return;
        }

        button.off(Node.EventType.TOUCH_START);
        button.off(Node.EventType.TOUCH_END);
        button.off(Node.EventType.TOUCH_CANCEL);
        button.off(Node.EventType.TOUCH_MOVE);

        this.stopAnimation(button);

        // 清理保存的原始缩放值
        this.originalScales.delete(button);
    }
}
