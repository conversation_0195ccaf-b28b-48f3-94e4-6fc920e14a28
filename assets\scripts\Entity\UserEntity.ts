
export class User {
    userId: number; // 用户ID
    username: string; // 用户名
    level: number; // 等级
    exp: number; // 经验值
    marbleCount: number; // 弹珠数量
    province: string; // 用户所在省份
    piece: number; // 普通碎片数量
    limitedPiece: number; // 限定碎片数量
    sharedDrawCount: number; // 共享抽取次数（皮肤和特效宝箱共用保底）
    turntableDrawCount: number; // 转盘抽取次数（70抽保底）
    lotteryTickets: number; // 特殊抽奖券数量
    signInDays: number; // 连续签到天数
    signStatus: { day: number, status1: number, status2: number }[]; // 签到状态数组
    gameCount: number; // 游戏对局次数
    beginnerTaskStatus: { taskId: number, completed: boolean, claimed: boolean }[]; // 新手任务状态
    createTime: string; // 创建时间
    lastLoginTime: string; // 最后登录时间
    ownedMarbles: Record<string, number>; // 拥有的弹珠
    ownedSkins: number[]; // 拥有的皮肤

    ownedLaunchEffects: number[]; // 拥有的发射特效
    ownedCollisionEffects: number[]; // 拥有的碰撞特效
    ownedTrailEffects: number[]; // 拥有的拖尾特效
    timedSkins: { skinId: number, expireTime: string }[]; // 限时皮肤
    timedLaunchEffects: { effectId: number, expireTime: string }[]; // 限时发射特效
    timedCollisionEffects: { effectId: number, expireTime: string }[]; // 限时碰撞特效
    timedTrailEffects: { effectId: number, expireTime: string }[]; // 限时拖尾特效
    currentSkin: number; // 当前皮肤
    currentLaunchEffect: number; // 当前发射特效
    currentCollisionEffect: number; // 当前碰撞特效
    currentTrailEffect: number; // 当前拖尾特效


    constructor(data: any) {
        this.userId = data.userId;
        this.username = data.username;
        this.level = data.level;
        this.exp = data.exp;
        this.marbleCount = data.marbleCount;
        this.province = data.province || '未设置';
        this.piece = data.piece || 0;
        this.limitedPiece = data.limitedPiece || 0;
        this.sharedDrawCount = data.sharedDrawCount || 0;
        this.turntableDrawCount = data.turntableDrawCount || 0;
        this.lotteryTickets = data.lotteryTickets || 0;
        this.signInDays = data.signInDays || 0;
        this.signStatus = data.signStatus;
        this.gameCount = data.gameCount || 0;
        this.beginnerTaskStatus = data.beginnerTaskStatus;
        this.createTime = data.createTime;
        this.lastLoginTime = data.lastLoginTime;
        this.ownedMarbles = data.ownedMarbles || {};
        this.ownedSkins = data.ownedSkins || [];
        this.ownedLaunchEffects = data.ownedLaunchEffects || [1]; // 默认拥有基础发射特效
        this.ownedCollisionEffects = data.ownedCollisionEffects || [101]; // 默认拥有基础碰撞特效
        this.ownedTrailEffects = data.ownedTrailEffects || [201]; // 默认拥有基础拖尾特效
        this.timedSkins = data.timedSkins || [];
        this.timedLaunchEffects = data.timedLaunchEffects || [];
        this.timedCollisionEffects = data.timedCollisionEffects || [];
        this.timedTrailEffects = data.timedTrailEffects || [];
        this.currentSkin = data.currentSkin || 1;
        this.currentLaunchEffect = data.currentLaunchEffect || 1;
        this.currentCollisionEffect = data.currentCollisionEffect || 101;
        this.currentTrailEffect = data.currentTrailEffect || 201;
    }

    // 保存用户数据到本地存储
    save(): boolean {
        try {
            const userData = this.toJSON();
            localStorage.setItem(`user_${this.userId}`, JSON.stringify(userData));
            console.log(`用户${this.username}数据已保存`);
            return true;
        } catch (error) {
            console.error("保存用户数据失败:", error);
            return false;
        }
    }

    // 从本地存储加载用户数据
    static load(userId: number): User | null {
        try {
            const userData = localStorage.getItem(`user_${userId}`);
            if (userData) {
                const data = JSON.parse(userData);
                return new User(data);
            }
            return null;
        } catch (error) {
            console.error("加载用户数据失败:", error);
            return null;
        }
    }

    // 转换为JSON对象
    toJSON(): any {
        return {
            userId: this.userId,
            username: this.username,
            level: this.level,
            exp: this.exp,
            marbleCount: this.marbleCount,
            province: this.province,
            piece: this.piece,
            limitedPiece: this.limitedPiece,
            sharedDrawCount: this.sharedDrawCount,
            turntableDrawCount: this.turntableDrawCount,
            signStatus: this.signStatus,
            lotteryTickets: this.lotteryTickets,
            signInDays: this.signInDays,
            gameCount: this.gameCount,
            beginnerTaskStatus: this.beginnerTaskStatus,
            createTime: this.createTime,
            lastLoginTime: new Date().toISOString(), // 更新最后登录时间
            ownedMarbles: this.ownedMarbles,
            ownedSkins: this.ownedSkins,
            ownedLaunchEffects: this.ownedLaunchEffects,
            ownedCollisionEffects: this.ownedCollisionEffects,
            ownedTrailEffects: this.ownedTrailEffects,
            timedSkins: this.timedSkins,
            timedLaunchEffects: this.timedLaunchEffects,
            timedCollisionEffects: this.timedCollisionEffects,
            timedTrailEffects: this.timedTrailEffects,
            currentSkin: this.currentSkin,
            currentLaunchEffect: this.currentLaunchEffect,
            currentCollisionEffect: this.currentCollisionEffect,
            currentTrailEffect: this.currentTrailEffect
        };
    }

    // 添加经验值并检查升级（带保存）
    addExp(expAmount: number): { levelUp: boolean, newLevel: number, expGained: number } {
        const oldLevel = this.level;
        this.exp += expAmount;
        
        // 检查是否需要升级
        let levelUp = false;
        let levelsGained = 0;
        
        while (this.exp >= 400) {
            this.exp -= 400; // 扣除升级所需经验值
            this.level += 1;
            levelsGained += 1;
            levelUp = true;
            console.log(`恭喜升级！当前等级：${this.level}`);
        }
        
        this.save();
        
        return {
            levelUp: levelUp,
            newLevel: this.level,
            expGained: expAmount
        };
    }



    // 添加弹珠数量（带保存）
    addMarbles(count: number): boolean {
        this.marbleCount += count;
        return this.save();
    }

    // 扣除弹珠数量（带验证和保存）
    deductMarbles(count: number): boolean {
        if (this.marbleCount < count) {
            console.warn(`弹珠不足！需要${count}颗，当前${this.marbleCount}颗`);
            return false;
        }
        this.marbleCount -= count;
        return this.save();
    }

    // 添加普通碎片数量（带保存）
    addPieces(count: number): boolean {
        this.piece += count;
        console.log(`获得${count}个普通碎片，当前数量：${this.piece}`);
        return this.save();
    }

    // 添加限定碎片数量（带保存）
    addLimitedPieces(count: number): boolean {
        this.limitedPiece += count;
        console.log(`获得${count}个限定碎片，当前数量：${this.limitedPiece}`);
        return this.save();
    }

    // 扣除碎片数量（带验证和保存）
    deductPieces(count: number): boolean {
        if (this.piece < count) {
            console.warn(`碎片不足！需要${count}个，当前${this.piece}个`);
            return false;
        }
        this.piece -= count;
        console.log(`消耗${count}个碎片，剩余碎片数量：${this.piece}`);
        return this.save();
    }

    // 检查碎片是否足够
    hasSufficientPieces(count: number): boolean {
        return this.piece >= count;
    }

    // 签到方法
    signIn(day: number, isAdvanced: boolean): boolean {

        const signData = this.signStatus.find(s => s.day === day);

        if (isAdvanced) {
            if (signData.status2 === 1) {
                console.warn('今日高级奖励已领取');
                return false;
            }
            signData.status2 = 1;
            this.lotteryTickets += 1; // 高级奖励
            console.log('高级签到成功，获得1张抽奖券');
        } else {
            if (signData.status1 === 1) {
                console.warn('今日普通奖励已领取');
                return false;
            }
            signData.status1 = 1;
            this.lotteryTickets += 1; // 普通奖励
            console.log('普通签到成功，获得1张抽奖券');
        }

        // 更新连续签到天数
        this.updateSignInDays();
        this.save();
        return true;
    }

    // 更新连续签到天数
    private updateSignInDays() {
        let consecutiveDays = 0;
        for (let i = 0; i < this.signStatus.length; i++) {
            const signData = this.signStatus[i];
            if (signData.status1 === 1 || signData.status2 === 1) {
                consecutiveDays++;
            } else {
                break;
            }
        }
        this.signInDays = consecutiveDays;
    }

    // 检查某天是否可以签到
    canSignIn(day: number, isAdvanced: boolean): boolean {
        const signData = this.signStatus.find(s => s.day === day);
        if (!signData) return false;

        if (isAdvanced) {
            return signData.status2 === 0;
        } else {
            return signData.status1 === 0;
        }
    }

    // 获取签到状态
    getSignStatus(day: number): { status1: number, status2: number } | null {
        const signData = this.signStatus.find(s => s.day === day);
        return signData ? { status1: signData.status1, status2: signData.status2 } : null;
    }

    // 检查是否拥有皮肤（包括永久和限时）
    hasSkin(skinId: number): boolean {
        // 检查永久皮肤
        if (this.ownedSkins.includes(skinId)) {
            return true;
        }
        // 检查限时皮肤
        const timedSkin = this.timedSkins.find(ts => ts.skinId === skinId);
        if (timedSkin) {
            // 检查是否过期
            const now = new Date();
            const expireTime = new Date(timedSkin.expireTime);
            return now < expireTime;
        }
        return false;
    }



    // 检查是否拥有发射特效（包括永久和限时）
    hasLaunchEffect(effectId: number): boolean {
        // 检查永久特效
        if (this.ownedLaunchEffects.includes(effectId)) {
            return true;
        }
        // 检查限时特效
        const timedEffect = this.timedLaunchEffects.find(te => te.effectId === effectId);
        if (timedEffect) {
            // 检查是否过期
            const now = new Date();
            const expireTime = new Date(timedEffect.expireTime);
            return now < expireTime;
        }
        return false;
    }

    // 检查是否拥有碰撞特效（包括永久和限时）
    hasCollisionEffect(effectId: number): boolean {
        // 检查永久特效
        if (this.ownedCollisionEffects.includes(effectId)) {
            return true;
        }
        // 检查限时特效
        const timedEffect = this.timedCollisionEffects.find(te => te.effectId === effectId);
        if (timedEffect) {
            // 检查是否过期
            const now = new Date();
            const expireTime = new Date(timedEffect.expireTime);
            return now < expireTime;
        }
        return false;
    }

    // 检查是否拥有拖尾特效（包括永久和限时）
    hasTrailEffect(effectId: number): boolean {
        // 检查永久特效
        if (this.ownedTrailEffects.includes(effectId)) {
            return true;
        }
        // 检查限时特效
        const timedEffect = this.timedTrailEffects.find(te => te.effectId === effectId);
        if (timedEffect) {
            // 检查是否过期
            const now = new Date();
            const expireTime = new Date(timedEffect.expireTime);
            return now < expireTime;
        }
        return false;
    }

    // 添加限时皮肤或延长时间
    addTimedSkin(skinId: number, daysToAdd: number = 7): void {
        const existingTimed = this.timedSkins.find(ts => ts.skinId === skinId);
        const now = new Date();

        if (existingTimed) {
            // 如果已有限时皮肤，延长时间
            const currentExpire = new Date(existingTimed.expireTime);
            const newExpire = currentExpire > now ? currentExpire : now;
            newExpire.setDate(newExpire.getDate() + daysToAdd);
            existingTimed.expireTime = newExpire.toISOString();
            console.log(`限时皮肤${skinId}时间延长${daysToAdd}天`);
        } else {
            // 添加新的限时皮肤
            const expireTime = new Date(now);
            expireTime.setDate(expireTime.getDate() + daysToAdd);
            this.timedSkins.push({
                skinId: skinId,
                expireTime: expireTime.toISOString()
            });
            console.log(`获得限时皮肤${skinId}，有效期${daysToAdd}天`);
        }
    }



    // 添加限时发射特效或延长时间
    addTimedLaunchEffect(effectId: number, daysToAdd: number = 7): void {
        const existingTimed = this.timedLaunchEffects.find(te => te.effectId === effectId);
        const now = new Date();

        if (existingTimed) {
            const currentExpire = new Date(existingTimed.expireTime);
            const newExpire = currentExpire > now ? currentExpire : now;
            newExpire.setDate(newExpire.getDate() + daysToAdd);
            existingTimed.expireTime = newExpire.toISOString();
            console.log(`限时发射特效${effectId}时间延长${daysToAdd}天`);
        } else {
            const expireTime = new Date(now);
            expireTime.setDate(expireTime.getDate() + daysToAdd);
            this.timedLaunchEffects.push({
                effectId: effectId,
                expireTime: expireTime.toISOString()
            });
            console.log(`获得限时发射特效${effectId}，有效期${daysToAdd}天`);
        }
    }

    // 添加限时碰撞特效或延长时间
    addTimedCollisionEffect(effectId: number, daysToAdd: number = 7): void {
        const existingTimed = this.timedCollisionEffects.find(te => te.effectId === effectId);
        const now = new Date();

        if (existingTimed) {
            const currentExpire = new Date(existingTimed.expireTime);
            const newExpire = currentExpire > now ? currentExpire : now;
            newExpire.setDate(newExpire.getDate() + daysToAdd);
            existingTimed.expireTime = newExpire.toISOString();
            console.log(`限时碰撞特效${effectId}时间延长${daysToAdd}天`);
        } else {
            const expireTime = new Date(now);
            expireTime.setDate(expireTime.getDate() + daysToAdd);
            this.timedCollisionEffects.push({
                effectId: effectId,
                expireTime: expireTime.toISOString()
            });
            console.log(`获得限时碰撞特效${effectId}，有效期${daysToAdd}天`);
        }
    }

    // 添加限时拖尾特效或延长时间
    addTimedTrailEffect(effectId: number, daysToAdd: number = 7): void {
        const existingTimed = this.timedTrailEffects.find(te => te.effectId === effectId);
        const now = new Date();

        if (existingTimed) {
            const currentExpire = new Date(existingTimed.expireTime);
            const newExpire = currentExpire > now ? currentExpire : now;
            newExpire.setDate(newExpire.getDate() + daysToAdd);
            existingTimed.expireTime = newExpire.toISOString();
            console.log(`限时拖尾特效${effectId}时间延长${daysToAdd}天`);
        } else {
            const expireTime = new Date(now);
            expireTime.setDate(expireTime.getDate() + daysToAdd);
            this.timedTrailEffects.push({
                effectId: effectId,
                expireTime: expireTime.toISOString()
            });
            console.log(`获得限时拖尾特效${effectId}，有效期${daysToAdd}天`);
        }
    }

    // 扣除抽奖券
    deductLotteryTickets(count: number): boolean {
        if (this.lotteryTickets < count) {
            console.warn(`抽奖券不足！需要${count}张，当前${this.lotteryTickets}张`);
            return false;
        }
        this.lotteryTickets -= count;
        console.log(`消耗${count}张抽奖券，剩余${this.lotteryTickets}张`);
        return this.save();
    }


    // 检查并更新新手任务完成状态
    updateBeginnerTaskStatus(): void {
        for (let task of this.beginnerTaskStatus) {
            if (!task.completed && this.gameCount >= task.taskId) {
                task.completed = true;
                console.log(`新手任务完成：完成${task.taskId}局游戏`);
            }
        }
    }

    // 领取新手任务奖励
    claimBeginnerTaskReward(taskId: number): { success: boolean, reward: any } {
        const task = this.beginnerTaskStatus.find(t => t.taskId === taskId);
        if (!task) {
            return { success: false, reward: null };
        }

        if (!task.completed) {
            console.warn(`任务未完成：需要完成${taskId}局游戏，当前${this.gameCount}局`);
            return { success: false, reward: null };
        }

        if (task.claimed) {
            console.warn(`奖励已领取`);
            return { success: false, reward: null };
        }

        // 根据任务ID给予不同奖励
        let reward = this.getBeginnerTaskReward(taskId);
        if (reward) {
            task.claimed = true;
            this.applyBeginnerTaskReward(reward);
            this.save();
            return { success: true, reward };
        }

        return { success: false, reward: null };
    }

    // 获取新手任务奖励配置
    private getBeginnerTaskReward(taskId: number): any {
        const rewards = {
            1: { type: 'marble', count: 1, name: '弹珠' },
            3: { type: 'skinCard', count: 1, name: '皮肤体验卡' },
            5: { type: 'marble', count: 1, name: '弹珠' },
            8: { type: 'effectCard', count: 1, name: '特效体验卡' },
            10: { type: 'marble', count: 2, name: '弹珠' },
            20: { type: 'permanentSkin', skinId: 4, name: '永久四星弹珠皮肤' }
        };
        return rewards[taskId] || null;
    }

    // 应用新手任务奖励
    private applyBeginnerTaskReward(reward: any): void {
        switch (reward.type) {
            case 'marble':
                this.addMarbles(reward.count);
                console.log(`获得${reward.count}颗弹珠`);
                break;
            case 'skinCard':
                // TODO: 实现皮肤体验卡逻辑
                console.log(`获得${reward.count}张皮肤体验卡`);
                break;
            case 'effectCard':
                // TODO: 实现特效体验卡逻辑
                console.log(`获得${reward.count}张特效体验卡`);
                break;
            case 'permanentSkin':
                if (!this.ownedSkins.includes(reward.skinId)) {
                    this.ownedSkins.push(reward.skinId);
                }
                console.log(`获得永久皮肤：${reward.name}`);
                break;
        }
    }
}


