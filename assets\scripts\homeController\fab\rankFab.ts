import { _decorator, Component, Label, Node, resources, Sprite, SpriteFrame } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('rankFab')
export class rankFab extends Component {

    @property(Sprite)
    rankSprite: Sprite = null;

    @property(Label)
    rankLabel: Label = null;

    @property(Label)
    nameLabel: Label = null;

    @property(Sprite)
    danSprite: Sprite = null;

    start() {

    }

    set(rank: number, name: string, dan: string) {
        if (rank <= 3) {
            if (rank == 1) {
                resources.load("homeUI/rank1/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                    this.rankSprite.spriteFrame = spriteFrame;
                })
            }
            else if (rank == 2) {
                resources.load("homeUI/rank2/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                    this.rankSprite.spriteFrame = spriteFrame;
                })
            }
            else if (rank == 3) {
                resources.load("homeUI/rank3/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                    this.rankSprite.spriteFrame = spriteFrame;
                })
            }
            this.rankLabel.node.active = false;
        }
        else {
            this.rankLabel.string = rank.toString();
            this.rankSprite.node.active = false;
        }

        this.nameLabel.string = name;
        resources.load("homeUI/" + dan + "/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            this.danSprite.spriteFrame = spriteFrame;
        })
    }

    update(deltaTime: number) {

    }
}


