import { _decorator, Component, Label, Node, RigidBody2D, Vec2 } from 'cc';
// import { WeatherType, getWeather } from '../../Data/WeatherData';
import { Floor } from './Floor';
import { TargetArea } from './TargetArea';
import { PlayeMarble } from './PlayeMarble';
import { AiMarble } from './AiMarble';
const { ccclass, property } = _decorator;

/**
 * 提供不同天气效果
 */

@ccclass('Weather')
export class Weather extends Component {

    @property(Floor)
    floor: Floor = null;

    @property(TargetArea)
    targetArea: TargetArea = null;

    @property(PlayeMarble)
    playerMarble: PlayeMarble = null;

    @property(AiMarble)
    aiMarble: AiMarble = null;

  

    private direction: Vec2 = new Vec2(0, 1);

    private windForce: number = 1;

    private weatherCircleList: number[] = [0, 1, 1, 0, 0, 0, 1];

    private currentWeatherIndex: number = 0;

    private currentWeatherTime: number = 0;

    start() {   
  
    }


    setWeatherParam(direction: Vec2, windForce: number) {
        this.direction = direction;
        this.windForce = windForce;
    }


    public applyResistanceToMovingMarbles(marble: Node, direction: Vec2) {
        let rb2d = marble.getComponent(RigidBody2D);

        //检测速度是否小于0.1
        if (rb2d.linearVelocity.length() < 0.1) return; // 速度过小，不施加力

        // 改进的风力计算：基于速度和风向的更真实模拟
        const velocity = rb2d.linearVelocity;
        const directionVec = direction.normalize();

        // 计算风力对弹珠的影响
        const windEffect = directionVec.clone().multiplyScalar(this.windForce * 0.3);

        // 计算空气阻力（与速度平方成正比，方向相反）
        const dragCoefficient = 0.02;
        const airResistance = velocity.clone()
            .normalize()
            .multiplyScalar(-dragCoefficient * velocity.lengthSqr());

        // 合并风力和空气阻力
        const totalForce = windEffect.add(airResistance);

        // 应用合力
        rb2d.applyForceToCenter(totalForce, true);
    }

    update(_deltaTime: number) {
        this.currentWeatherTime += _deltaTime; // 累加时间计数器
        // 循环天气效果
        if (this.currentWeatherTime >= 15) {
            this.currentWeatherIndex = (this.currentWeatherIndex + 1) % this.weatherCircleList.length;
            this.currentWeatherTime = 0;
            // const weatherType = getWeather(this.weatherCircleList[this.currentWeatherIndex]);
            // this.direction = weatherType.windDirection.clone();
            // this.windForce = weatherType.windForce;
            // this.floor.setWeatherFriction(weatherType.weatherFriction);
            // console.log(`天气效果：${weatherType.weatherName}`);
            // this.updateUI();
        }
        
        // 如果所有弹珠都停止运动
        if (!this.floor.isAllMarbleStop()) {

            // 对所有运动的弹珠应用天气效果
            this.applyResistanceToMovingMarbles(this.playerMarble.node, this.direction);
            this.applyResistanceToMovingMarbles(this.aiMarble.node, this.direction);

            // 对目标弹珠应用天气效果
            const targetMarbles = this.targetArea.targetMarbles;
            for (const targetMarble of targetMarbles) {
                if (targetMarble && targetMarble.isValid) {
                    this.applyResistanceToMovingMarbles(targetMarble, this.direction);
                }
            }
        }
    
    }
}


