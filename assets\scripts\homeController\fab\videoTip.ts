import { _decorator, Button, Component, Label, Node } from 'cc';
const { ccclass, property } = _decorator;

/*
    弹珠不足，观看视频提醒
*/
@ccclass('videoTip')
export class videoTip extends Component {
    start() {
        this.node.active = false;
        this.node.getChildByName('back').getComponent(Button).node.on('click', this.back, this);
        this.node.getChildByName('watch').getComponent(Button).node.on('click', this.watch, this);
    }
    show(restCount: number) {
        this.node.active = true;
        this.node.getChildByName('restCount').getComponent(Label).string = restCount.toString();
    }
    back() {
        this.node.active = false;
    }
    watch() {
        console.log('watch');
    }
    update(deltaTime: number) {

    }
}


