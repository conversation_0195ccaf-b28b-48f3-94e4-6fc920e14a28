import { _decorator, <PERSON><PERSON>, Component, director, Label, Node } from 'cc';
import { DataManager } from '../../Managers/DataManager';
const { ccclass, property } = _decorator;

@ccclass('postionFab')
export class postionFab extends Component {

    private cityName: string = null;
    start() {

    }
    setCityName(cityName: string) {
        this.cityName = cityName;
        this.node.getChildByName('cityname').getComponent(Label).string = cityName;

        this.node.on('click', this.onClick, this);
    }
    onClick() {
        console.log('选择省份:', this.cityName);

        // 获取数据管理器实例
        const dataManager = DataManager.getInstance();
        if (dataManager && dataManager.User) {
            // 更新用户的省份属性
            dataManager.User.province = this.cityName;

            // 保存用户数据到本地存储
            dataManager.User.save();

            console.log('用户省份已更新为:', this.cityName);
            // 发送事件
            director.emit('provinceUpdated');
            this.node.scene.getChildByName('Canvas').getChildByName('postion').active = false;
        } else {
            console.error('无法获取用户数据');
        }
    }

    update(deltaTime: number) {

    }
}


