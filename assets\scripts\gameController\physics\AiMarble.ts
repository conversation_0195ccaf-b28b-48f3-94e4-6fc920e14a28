import { _decorator, Component, director, Node, resources, RigidBody2D, Sprite, SpriteFrame, Vec2, Vec3, PhysicsSystem2D, ERaycast2DType } from 'cc';
import { TargetArea } from './TargetArea';
const { ccclass, property } = _decorator;

/*
    实现ai智能弹射
    提供检测是否停止的方法
    骰骰子的功能
    隐藏和显示的功能
*/

@ccclass('AiMarble')
export class AiMarble extends Component {

    @property(Vec2)
    private launchDirection: Vec2 = new Vec2(0, -1300);

    @property
    private shootForce: number = 200;

    start() {
        this.node.setPosition(new Vec3(this.launchDirection.x, this.launchDirection.y, 0));
    }

    setAiMarbleParam( marbleSpritename: string) {

        marbleSpritename = "gameUI/" + marbleSpritename + "/spriteFrame";
        resources.load(marbleSpritename, SpriteFrame, (err, spriteFrame) => {
            this.node.getComponent(Sprite).spriteFrame = spriteFrame;
            // console.log("加载AI弹珠图片", err);
        })

    }

    //骰骰子
    public rollDice() {
        return Math.floor(Math.random() * 6) + 1;
    }

    //智能弹射 参数所有目标节点和玩家弹珠位置
    public smartLaunch(targetArea: TargetArea, playerMarbleNode: Node) {
        const targetMarbles = targetArea.targetMarbles;
        const obstacles = targetArea.obstacles;

        const rigidBody = this.node.getComponent(RigidBody2D);
        const currentPosition = this.node.position;
        const bestTarget = this.selectBestTarget(targetMarbles, currentPosition);
        if (!bestTarget) {
            console.log("AI: 无法找到合适的目标");
            return;
        }

        const { direction, power } = this.calculateLaunchParams(currentPosition, bestTarget.position);
        console.log(`AI发射 - 目标: (${bestTarget.position.x.toFixed(2)}, ${bestTarget.position.y.toFixed(2)}), 方向: (${direction.x.toFixed(2)}, ${direction.y.toFixed(2)}), 力度: ${power.toFixed(2)}`);

        const force = direction.clone().multiplyScalar(power * this.shootForce);
        rigidBody.applyLinearImpulse(force, Vec2.ZERO, true);

        director.emit('ai-marble-launched', this);
    }
    // 计算发射参数
    private calculateLaunchParams(currentPos: Vec3, targetPos: Vec3): { direction: Vec2, power: number } {
        const distance = Vec3.distance(currentPos, targetPos);
        const direction3D = targetPos.subtract(currentPos).normalize();
        const direction = new Vec2(direction3D.x, direction3D.y);

        // 根据距离动态调整力度，参考玩家的力度范围
        let power: number;

        if (distance < 200) {
            power = 0.3 + Math.random() * 0.2; // 近距离：30%-50%
        } else if (distance < 500) {
            power = 0.5 + Math.random() * 0.3; // 中距离：50%-80%
        } else {
            power = 0.7 + Math.random() * 0.3; // 远距离：70%-100%
        }

        // 添加少量随机性，让AI不那么完美
        const randomAngle = (Math.random() - 0.5) * 0.2; // ±0.1弧度的随机偏移
        const cos = Math.cos(randomAngle);
        const sin = Math.sin(randomAngle);
        const rotatedDirection = new Vec2(
            direction.x * cos - direction.y * sin,
            direction.x * sin + direction.y * cos
        );

        return { direction: rotatedDirection.normalize(), power };
    }
    // 选择最佳目标
    private selectBestTarget(targetMarbles: Node[], currentPosition: Vec3): Node | null {
        let bestTarget: Node | null = null;
        let bestScore = -1;

        for (const target of targetMarbles) {
            if (!target || !target.isValid) continue;

            const distance = Vec3.distance(currentPosition, target.position);

            // 评分系统：距离越近分数越高，避免过远的目标
            let score = 1000 / (distance + 100); // 基础距离分数

            // 优先选择更容易击中的目标（距离适中）
            if (distance > 200 && distance < 800) {
                score *= 1.5; // 中等距离加分
            }

            // 避免选择过近的目标（可能导致自己进入目标区域）
            if (distance < 150) {
                score *= 0.3;
            }

            if (score > bestScore) {
                bestScore = score;
                bestTarget = target;
            }
        }

        return bestTarget;
    }



    //实现检测是否停止运动的方法
    public isStopped(): boolean {
        const rigidBody = this.node.getComponent(RigidBody2D);

        const velocity = rigidBody.linearVelocity;
        // console.log(this.node.name + '速度：' + velocity);
        return velocity.length() < 0.1;
    }

    //显示节点
    public init() {
        this.node.active = true;

    }
    //隐藏节点
    public hide() {
        this.node.active = false;
    }

    update(_deltaTime: number) {
        //检查速度
        // console.log(this.node.name + '速度：' + this.node.getComponent(RigidBody2D).linearVelocity);
    }

    // 检查路径上是否有障碍物
    private checkPathObstacles(startPos: Vec3, targetPos: Vec3): boolean {
        const direction = targetPos.subtract(startPos);
        const distance = direction.length();
        const normalizedDir = direction.normalize();

        // 使用射线检测
        const results = PhysicsSystem2D.instance.raycast(
            new Vec2(startPos.x, startPos.y),
            new Vec2(startPos.x + normalizedDir.x * distance, startPos.y + normalizedDir.y * distance),
            ERaycast2DType.Closest
        );

        // 检查是否有障碍物碰撞体
        for (const result of results) {
            const colliderNode = result.collider.node;
            // 检查是否是障碍物（排除目标弹珠和边界墙）
            if (colliderNode.name.includes('Obstacle')) {
                return true; // 路径上有障碍物
            }
        }

        return false; // 路径畅通
    }
}


