import { _decorator, Button, Component, instantiate, Label, Node, Prefab } from 'cc';
import { result } from './result';
import { ButtonAnimationUtils } from '../../Utils/ButtonAnimationUtils';
const { ccclass, property } = _decorator;

/*
    恭喜获得弹窗预制体
    显示获得物品
*/
@ccclass('resultTip')
export class resultTip extends Component {

    @property(Prefab)
    resultPrefab: Prefab = null;

    @property(Node)
    container: Node = null;

    start() {
        const closeButton = this.node.getChildByName('SpriteSplash');

        // 添加按钮动画效果
        ButtonAnimationUtils.setupButtonInteraction(closeButton);

        closeButton.getComponent(Button).node.on('click', this.closeDialog, this);
    }

    closeDialog() {
        this.node.destroy();
    }

    set(rewards: Array<{name: string, count: number}>) {
        if (!this.resultPrefab || !this.container) return;
        
        // 清空容器
        this.container.removeAllChildren();
        
        // 为每个奖励创建预制体
        rewards.forEach(reward => {
            const resultNode = instantiate(this.resultPrefab);
            const resultComponent = resultNode.getComponent(result);
            if (resultComponent) {
                resultComponent.set(reward.name, reward.count);
            }
            this.container.addChild(resultNode);
        });
    }

    update(deltaTime: number) {
        
    }
}


