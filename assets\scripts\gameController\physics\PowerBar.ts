import { _decorator, Camera, Component, find, Node, ProgressBar, UITransform, v3, Vec2, Vec3 } from 'cc';
import { PlayeMarble } from './PlayeMarble';
const { ccclass, property } = _decorator;

/*
    动态显示力度条，提供获取力度值的方法
*/
@ccclass('PowerBar')
export class PowerBar extends Component {
    @property(ProgressBar)
    private progressBar: ProgressBar = null;

    @property(Node)
    private cameraNode: Node = null;

    private currentPower: number = 0.5; // 默认值为0.5

    start() {
        
    }

    //初始化函数
    init() {
        this.node.active = true;

        // 初始化进度条
        this.progressBar.progress = this.currentPower;

    }

    //隐藏函数
    hide() {
        this.node.active = false;


    }
     // 获取当前力度值
    public getCurrentPower(): number {
        return this.currentPower;
    }

    // 直接设置力度值（用于拖拽控制）
    public setPower(power: number): void {
        this.currentPower = Math.min(1, Math.max(0, power));
        this.progressBar.progress = this.currentPower;
    }

    // 设置力度条位置（摄像机左下角）
    setPositionRelativeToCamera(aimDirection?: Vec2) {
        const Camera_Pos = this.cameraNode.position;
        // console.log(`Camera Position: (${Camera_Pos.x}, ${Camera_Pos.y})`);
        this.node.setPosition(Camera_Pos.x - 400, Camera_Pos.y - 700, 0);
    }

    update(){
        this.setPositionRelativeToCamera();
    }

   
}


