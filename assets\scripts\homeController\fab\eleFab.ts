import { _decorator, Component, Node, Label } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('eleFab')
export class eleFab extends Component {
    start() {

    }

    set(name: string, count: number) {
        this.node.getChildByName("name").getComponent(Label).string = name;
        this.node.getChildByName("count").getComponent(Label).string = count.toString();
    }
    update(deltaTime: number) {
        
    }
}


