import { _decorator, Component, Label, Sprite, Color } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('turnFeb')
export class turnFeb extends Component {

    setName(name: string) {
        const nameLabel = this.node.getChildByName('name');
        if (nameLabel) nameLabel.getComponent(Label).string = name;
    }

    setItemData(data: any) {
        const itemName = data.skinName || data.effectName;
        const prefix = data.isTimed ? "[限时]" : "";
        this.setName(prefix + itemName);

    }
}


