import { _decorator, Button, Component, Prefab, ScrollView, instantiate, resources, JsonAsset, SpriteFrame, Sprite } from "cc";
import { DataManager } from "../Managers/DataManager";
import { eleFab } from "./fab/eleFab";
import { ButtonAnimationUtils } from "../Utils/ButtonAnimationUtils";
const { ccclass, property } = _decorator;

/*
    背包系统组件 - 管理玩家道具和物品展示

    主要功能：
    1. 道具分类：全部、工具、碎片、皮肤四个分类标签
    2. 物品展示：滚动视图显示玩家拥有的物品
    3. 筛选功能：根据分类筛选显示不同类型物品
    4. 物品详情：展示物品图标、数量等信息

    使用方式：
    - 通过BattlePage的背包按钮激活显示
    - 点击分类按钮切换不同物品类型
    - 使用数据管理器获取玩家物品数据
*/

@ccclass("backpack")
export class backpack extends Component {

    @property(Button)
    allButton: Button = null;

    @property(Button)
    toolButton: Button = null;

    @property(Button)
    pieceButton: Button = null;

    @property(Button)
    skinButton: Button = null;

    @property(Button)
    effectButton: Button = null;

    @property(Button)
    launchButton: Button = null;

    @property(Button)
    collisionButton: Button = null;

    @property(Button)
    trailButton: Button = null;

    @property(Prefab)
    elePrefab: Prefab = null;

    @property(ScrollView)
    scrollView: ScrollView = null;

    @property(Button)
    closeBtn: Button = null;

    private dataManager: DataManager = null;
    private skinsData: any[] = [];
    private launchEffectsData: any[] = [];
    private collisionEffectsData: any[] = [];
    private trailEffectsData: any[] = [];

    start() {
        this.dataManager = DataManager.getInstance();

        // 为所有按钮添加动画效果
        const allButtons = [
            this.allButton.node, this.toolButton.node, this.pieceButton.node,
            this.skinButton.node, this.effectButton.node, this.launchButton.node,
            this.collisionButton.node, this.trailButton.node, this.closeBtn.node
        ];

        allButtons.forEach(button => {
            if (button) {
                ButtonAnimationUtils.setupButtonInteraction(button);
            }
        });

        this.allButton.node.on("click", this.all, this);
        this.toolButton.node.on("click", this.tool, this);
        this.pieceButton.node.on("click", this.piece, this);
        this.skinButton.node.on("click", this.skin, this);
        this.effectButton.node.on("click", this.effect, this);
        this.launchButton.node.on("click", this.launch, this);
        this.collisionButton.node.on("click", this.collision, this);
        this.trailButton.node.on("click", this.trail, this);

        this.closeBtn.node.on("click", this.close, this);

        this.loadAllData();
    }

    loadAllData() {
        Promise.all([
            this.loadJsonAsset('data/skins'),
            this.loadJsonAsset('data/launchEffects'),
            this.loadJsonAsset('data/collisionEffects'),
            this.loadJsonAsset('data/trailEffects')
        ]).then(([skinsAsset, launchAsset, collisionAsset, trailAsset]) => {
            this.skinsData = (skinsAsset as JsonAsset).json.skins;
            this.launchEffectsData = (launchAsset as JsonAsset).json.launchEffects;
            this.collisionEffectsData = (collisionAsset as JsonAsset).json.collisionEffects;
            this.trailEffectsData = (trailAsset as JsonAsset).json.trailEffects;

            this.loadUserOwnData('all');
        }).catch(error => {
            console.error("加载背包数据失败:", error);
        });
    }

    loadJsonAsset(path: string): Promise<JsonAsset> {
        return new Promise((resolve, reject) => {
            resources.load(path, JsonAsset, (err, asset) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(asset);
                }
            });
        });
    }

    loadUserOwnData(type: string) {
        if (!this.dataManager || !this.dataManager.User) {
            console.error("用户数据未加载");
            return;
        }

        const user = this.dataManager.User;
        const contentNode = this.scrollView.content;
        contentNode.removeAllChildren();

        let items: any[] = [];

        if (type === "all") {
            // 显示所有拥有的物品
            items.push(...this.getOwnedSkins());
            items.push(...this.getOwnedEffects());
            items.push(...this.getOwnedTools());
            items.push(...this.getOwnedPieces());
        } else if (type === "tool") {
            items = this.getOwnedTools();
        } else if (type === "piece") {
            items = this.getOwnedPieces();
        } else if (type === "skin") {
            items = this.getOwnedSkins();
        } else if (type === "effect") {
            items = this.getOwnedEffects();
        }

        this.createItemCards(items);
    }

    getOwnedSkins(): any[] {
        const user = this.dataManager.User;
        const ownedSkins: any[] = [];

        // 获取永久皮肤
        user.ownedSkins.forEach(skinId => {
            const skinData = this.skinsData.find(skin => skin.skinId === skinId);
            if (skinData) {
                ownedSkins.push({
                    name: skinData.skinName,
                    count: 1
                });
            }
        });

        return ownedSkins;
    }

    getOwnedEffects(): any[] {
        const user = this.dataManager.User;
        const ownedEffects: any[] = [];

        // 获取发射特效
        user.ownedLaunchEffects.forEach(effectId => {
            const effectData = this.launchEffectsData.find(effect => effect.effectId === effectId);
            if (effectData) {
                ownedEffects.push({
                    name: effectData.effectName,
                    count: 1
                });
            }
        });

        // 获取碰撞特效
        user.ownedCollisionEffects.forEach(effectId => {
            const effectData = this.collisionEffectsData.find(effect => effect.effectId === effectId);
            if (effectData) {
                ownedEffects.push({
                    name: effectData.effectName,
                    count: 1
                });
            }
        });

        // 获取拖尾特效
        user.ownedTrailEffects.forEach(effectId => {
            const effectData = this.trailEffectsData.find(effect => effect.effectId === effectId);
            if (effectData) {
                ownedEffects.push({
                    name: effectData.effectName,
                    count: 1
                });
            }
        });

        return ownedEffects;
    }

    getOwnedTools(): any[] {
        const user = this.dataManager.User;
        const items: any[] = [];

        if (user.marbleCount > 0) {
            items.push({ name: "弹珠", count: user.marbleCount });
        }
        if (user.lotteryTickets > 0) {
            items.push({ name: "抽奖券", count: user.lotteryTickets });
        }

        return items;
    }

    getOwnedPieces(): any[] {
        const user = this.dataManager.User;
        const items: any[] = [];

        if (user.piece > 0) {
            items.push({ name: "普通碎片", count: user.piece });
        }
        if (user.limitedPiece > 0) {
            items.push({ name: "限定碎片", count: user.limitedPiece });
        }

        return items;
    }

    getOwnedLaunchEffects(): any[] {
        const user = this.dataManager.User;
        const ownedEffects: any[] = [];

        // 获取发射特效
        user.ownedLaunchEffects.forEach(effectId => {
            const effectData = this.launchEffectsData.find(effect => effect.effectId === effectId);
            if (effectData) {
                ownedEffects.push({
                    name: effectData.effectName,
                    count: 1
                });
            }
        });

        return ownedEffects;
    }

    getOwnedCollisionEffects(): any[] {
        const user = this.dataManager.User;
        const ownedEffects: any[] = [];

        // 获取碰撞特效
        user.ownedCollisionEffects.forEach(effectId => {
            const effectData = this.collisionEffectsData.find(effect => effect.effectId === effectId);
            if (effectData) {
                ownedEffects.push({
                    name: effectData.effectName,
                    count: 1
                });
            }
        });

        return ownedEffects;
    }

    getOwnedTrailEffects(): any[] {
        const user = this.dataManager.User;
        const ownedEffects: any[] = [];

        // 获取拖尾特效
        user.ownedTrailEffects.forEach(effectId => {
            const effectData = this.trailEffectsData.find(effect => effect.effectId === effectId);
            if (effectData) {
                ownedEffects.push({
                    name: effectData.effectName,
                    count: 1
                });
            }
        });

        return ownedEffects;
    }

    createItemCards(items: any[]) {
        if (!this.scrollView || !this.elePrefab) return;

        const contentNode = this.scrollView.content;

        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            const cardNode = instantiate(this.elePrefab);
            const cardComponent = cardNode.getComponent(eleFab);

            if (cardComponent) {
                cardComponent.set(item.name, item.count);
                contentNode.addChild(cardNode);
            }
        }
    }

    // 设置激活按钮状态
    setActiveButton(activeType: string) {
        const buttons = [
            { type: 'all', button: this.allButton },
            { type: 'tool', button: this.toolButton },
            { type: 'piece', button: this.pieceButton },
            { type: 'skin', button: this.skinButton },
            { type: 'effect', button: this.effectButton },
            { type: 'launch', button: this.launchButton },
            { type: 'collision', button: this.collisionButton },
            { type: 'trail', button: this.trailButton }
        ];

        buttons.forEach(item => {
            if (item.type === activeType) {
                // 设置为激活状态
                let activePath = "";
                if (item.type === 'launch' || item.type === 'collision' || item.type === 'trail') {
                    activePath = "homeUI/eactive/spriteFrame";
                } else {
                    activePath = "homeUI/active/spriteFrame";
                }

                resources.load(activePath, SpriteFrame, (err, spriteFrame) => {
                    if (!err && item.button) {
                        item.button.node.getComponent(Sprite).spriteFrame = spriteFrame;
                    }
                    // 打印错误
                    // console.error(err);
                });
            } else {
                // 设置为非激活状态
                let inactivePath = "";
                if (item.type === 'launch' || item.type === 'collision' || item.type === 'trail') {
                    inactivePath = "homeUI/enoactive/spriteFrame";
                } else {
                    inactivePath = "homeUI/noactive/spriteFrame";
                }

                resources.load(inactivePath, SpriteFrame, (err, spriteFrame) => {
                    if (!err && item.button) {
                        item.button.node.getComponent(Sprite).spriteFrame = spriteFrame;
                    }
                });
            }
        });
    }

    all() {
        this.loadUserOwnData("all");
        this.setActiveButton('all');
       
    }

    tool() {
        this.setActiveButton('tool');
        this.loadUserOwnData("tool");
    }

    piece() {
        this.setActiveButton('piece');
        this.loadUserOwnData("piece");
    }

    skin() {
        this.setActiveButton('skin');
        this.loadUserOwnData("skin");
    }

    effect() {
        this.setActiveButton('effect');
        this.loadUserOwnData("effect");
    }

    launch() {
        this.setActiveButton('launch');
        this.showSpecificEffects('launch');
    }

    collision() {
        this.setActiveButton('collision');
        this.showSpecificEffects('collision');
    }

    trail() {
        this.setActiveButton('trail');
        this.showSpecificEffects('trail');
    }

    // 显示特定类型的特效
    showSpecificEffects(effectType: string) {
        if (!this.dataManager || !this.dataManager.User) {
            console.error("用户数据未加载");
            return;
        }

        const contentNode = this.scrollView.content;
        contentNode.removeAllChildren();

        let items: any[] = [];

        if (effectType === 'launch') {
            items = this.getOwnedLaunchEffects();
        } else if (effectType === 'collision') {
            items = this.getOwnedCollisionEffects();
        } else if (effectType === 'trail') {
            items = this.getOwnedTrailEffects();
        }

        this.createItemCards(items);
    }

    close() {
        this.node.active = false;
    }
    update(deltaTime: number) {

    }
}
