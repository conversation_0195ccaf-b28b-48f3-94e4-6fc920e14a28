import { _decorator, Component, Node, Prefab, ProgressBar, instantiate, Button, Label } from 'cc';
import { DataManager } from '../Managers/DataManager';
import { User } from '../Entity/UserEntity';
import { rewardFeb } from './fab/rewardFab';
import { signFeb } from './fab/signFab';
import { resultTip } from './fab/resultTip';
const { ccclass, property } = _decorator;

/*
    活动系统组件 - 管理签到活动和奖励发放

    主要功能：
    1. 签到系统：每日签到记录和状态管理
    2. 奖励展示：普通奖励和高级奖励展示
    3. 进度跟踪：签到进度条和剩余时间显示
    4. 奖励发放：处理签到奖励的领取逻辑

    使用方式：
    - 通过BattlePage的活动按钮激活显示
    - 自动加载用户签到数据和奖励配置
    - 提供签到和奖励领取交互功能
*/

@ccclass('activity')
export class activity extends Component {

    @property(Prefab)
    rewardPrefab: Prefab = null;

    @property(Prefab)
    signPrefab: Prefab = null;

    @property(ProgressBar)
    progressBar: ProgressBar = null;

    @property(Node)
    normalRewardContainer: Node = null;

    @property(Node)
    advancedRewardContainer: Node = null;

    @property(Node)
    signContainer: Node = null;

    @property(Button)
    closeButton: Button = null;

    @property(Label)
    restTimeLabel: Label = null;

    @property(Prefab)
    resultTipPrefab: Prefab = null;

    private dataManager: DataManager = null;
    private currentUser: User = null;
    private timeUpdateCounter: number = 0;
    start() {
        this.dataManager = DataManager.getInstance();
        this.currentUser = this.dataManager.User;
        this.setupPrefab();
        this.updateProgressBar();
        this.updateRestTime();
        this.closeButton.node.on('click', this.onCloseClick, this);
    }

    setupPrefab() {
        this.setupRewardPrefabs();
        this.setupSignPrefabs();
    }

    // 设置奖励预制体
    setupRewardPrefabs() {
        // 清空容器
        this.normalRewardContainer.removeAllChildren();
        this.advancedRewardContainer.removeAllChildren();
        // 为每天创建两个奖励预制体（普通和高级）
        for (let day = 1; day <= 5; day++) {
            const signData = this.currentUser.signStatus.find(s => s.day === day);

            // 普通奖励
            const normalReward = instantiate(this.rewardPrefab);
            normalReward.getComponent(rewardFeb).init(false, signData.status1);;
            normalReward.on('click', () => this.onRewardClick(day, false), this);
            // 
            this.normalRewardContainer.addChild(normalReward);


            // 高级奖励
            const advancedReward = instantiate(this.rewardPrefab);
            advancedReward.getComponent(rewardFeb).init(true, signData.status2);
            advancedReward.on('click', () => this.onRewardClick(day, true), this);
            this.advancedRewardContainer.addChild(advancedReward);
        }
    }

    // 设置签到预制体
    setupSignPrefabs() {
        // 清空容器
        this.signContainer.removeAllChildren();
        // 为每天创建签到预制体
        for (let day = 1; day <= 5; day++) {
            const signData = this.currentUser.signStatus.find(s => s.day === day);
            const hasSignedToday = signData.status1 === 1 || signData.status2 === 1;

            const signNode = instantiate(this.signPrefab);
            signNode.getComponent(signFeb).init(hasSignedToday ? 1 : 0, day);
            this.signContainer.addChild(signNode);
        }
    }

    // 奖励点击事件
    onRewardClick(day: number, isAdvanced: boolean) {
        console.log(`点击第${day}天的${isAdvanced ? '高级' : '普通'}奖励`);

        // 检查是否可以领取
        if (!this.currentUser.canSignIn(day, isAdvanced)) {
            console.log('该奖励已领取或不可领取');
            return;
        }

        // 执行签到
        if (this.currentUser.signIn(day, isAdvanced)) {
            console.log(`签到成功！获得1张抽奖券，当前抽奖券数量：${this.currentUser.lotteryTickets}`);

            // 显示恭喜获得弹窗
            this.showResultTip("抽奖券", 1);

            // 刷新UI
            this.setupPrefab();
            this.updateProgressBar();

            // 如果是高级奖励，播放广告
            if (isAdvanced) {
                console.log('播放广告视频...');
                // TODO: 集成广告SDK
            }
        }
    }

    // 更新进度条
    updateProgressBar() {
        if (!this.progressBar) return;

        // 计算签到进度
        const totalDays = 5;
        const signedDays = this.currentUser.signInDays;
        this.progressBar.progress = signedDays / totalDays;

        console.log(`签到进度：${signedDays}/${totalDays} (${Math.round(this.progressBar.progress * 100)}%)`);
    }

    // 更新活动剩余时间
    updateRestTime() {
        if (!this.restTimeLabel || !this.currentUser) return;

        const currentTime = Date.now();
        const userCreateTime = new Date(this.currentUser.createTime).getTime();
        const activityDuration = 7 * 24 * 60 * 60 * 1000; // 7天的毫秒数

        // 计算已过去的时间
        const elapsedTime = currentTime - userCreateTime;

        // 计算剩余时间
        const remainingTime = activityDuration - elapsedTime;

        if (remainingTime <= 0) {
            this.restTimeLabel.string = "活动已结束";
            return;
        }

        // 转换为天、小时、分钟、秒
        const days = Math.floor(remainingTime / (24 * 60 * 60 * 1000));
        const hours = Math.floor((remainingTime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
        const minutes = Math.floor((remainingTime % (60 * 60 * 1000)) / (60 * 1000));
        const seconds = Math.floor((remainingTime % (60 * 1000)) / 1000);

        this.restTimeLabel.string = `活动剩余时间：${days}天${hours}小时${minutes}分${seconds}秒`;
    }

    onCloseClick() {
        this.node.active = false;
    }

    // 显示恭喜获得弹窗
    showResultTip(itemName: string, count: number) {
        if (!this.resultTipPrefab) return;

        const resultTipNode = instantiate(this.resultTipPrefab);
        const resultTipComponent = resultTipNode.getComponent(resultTip);
        if (resultTipComponent) {
            resultTipComponent.set([{name: itemName, count: count}]);
        }

        // 添加到场景根节点，确保在最上层显示
        this.node.addChild(resultTipNode);
    }

    update(deltaTime: number) {
        // 1秒更新一次剩余时间
        this.timeUpdateCounter += deltaTime;
        if (this.timeUpdateCounter >= 1.0) {
            this.timeUpdateCounter = 0;
            this.updateRestTime();
        }
    }
}


