import { _decorator, Component, instantiate, Node, Prefab, UITransform, RigidBody2D, Vec2 } from 'cc';
const { ccclass, property } = _decorator;

/*
    提供判断弹珠是否在区域内的方法
    提供生成目标弹珠的方法
    提供检测所有目标弹珠是否停止的方法
    检查并隐藏被弹出的节点-暂时
*/

@ccclass('TargetArea')
export class TargetArea extends Component {


    @property(Prefab)
    private targetPrefab: Prefab = null;

    // 障碍物预制体
    @property(Prefab)
    obstaclePrefab: Prefab = null;
    @property(Prefab)
    obstaclePrefab2: Prefab = null;
    @property(Prefab)
    obstaclePrefab3: Prefab = null;
    @property(Prefab)
    obstaclePrefab4: Prefab = null;
    @property(Prefab)
    obstaclePrefab5: Prefab = null;
    @property(Prefab)
    obstaclePrefab6: Prefab = null;

    // 所有目标弹珠
    public targetMarbles: Node[] = [];

    // 所有障碍物
    public obstacles: Node[] = [];

    start() {

    }

    public judgeInArea(target: Node): boolean {
        let targetPos = target.position;
        let targetAreaPos = this.node.position;
        let targetAreaSize = this.node.getComponent(UITransform).contentSize;

        console.log(`Target Position: (${targetPos.x}, ${targetPos.y})`);
        console.log(`Target Area Position: (${targetAreaPos.x}, ${targetAreaPos.y})`);
        console.log(`Target Area Size: (${targetAreaSize.width}, ${targetAreaSize.height})`);

        if (targetPos.x > targetAreaPos.x - targetAreaSize.x / 2 && targetPos.x < targetAreaPos.x + targetAreaSize.x / 2 &&
            targetPos.y > targetAreaPos.y - targetAreaSize.y / 2 && targetPos.y < targetAreaPos.y + targetAreaSize.y / 2
        ) {
            console.log('Target is inside the area.');
            return true;
        } else {
            console.log('Target is outside the area.');
            return false;
        }
    }



    public generateTargetMarbles(marbleCount: number): Node[] {
        // 获取对应数量的固定位置
        const fixedPositions = this.getFixedPositions(marbleCount);

        for (let i = 0; i < marbleCount; i++) {
            const targetNode = instantiate(this.targetPrefab);
            const pos = fixedPositions[i];

            targetNode.setPosition(pos.x, pos.y);
            this.node.addChild(targetNode);
            this.targetMarbles.push(targetNode);
        }

        return this.targetMarbles;
    }

    // 获取固定位置配置 - 支持8、10、20三种数量，避免重叠
    private getFixedPositions(count: number): { x: number, y: number }[] {
        // 目标区域尺寸: 1080×650，预留边距，弹珠间距至少80像素
        switch (count) {
            case 8:
                // 3-3-2 布局，紧凑排列
                return [
                    { x: -200, y: 250 }, { x: 0, y: 250 }, { x: 200, y: 250 },
                    { x: -200, y: 150 }, { x: 0, y: 150 }, { x: 200, y: 150 },
                    { x: -100, y: 50 }, { x: 100, y: 50 }
                ];
            case 10:
                // 3-3-3-1 布局
                return [
                    { x: -200, y: 250 }, { x: 0, y: 250 }, { x: 200, y: 250 },
                    { x: -200, y: 150 }, { x: 0, y: 150 }, { x: 200, y: 150 },
                    { x: -200, y: 50 }, { x: 0, y: 50 }, { x: 200, y: 50 },
                    { x: 0, y: -50 }
                ];
            case 20:
                // 4×5 网格布局，更大间距
                return [
                    { x: -300, y: 250 }, { x: -100, y: 250 }, { x: 100, y: 250 }, { x: 300, y: 250 },
                    { x: -300, y: 150 }, { x: -100, y: 150 }, { x: 100, y: 150 }, { x: 300, y: 150 },
                    { x: -300, y: 50 }, { x: -100, y: 50 }, { x: 100, y: 50 }, { x: 300, y: 50 },
                    { x: -300, y: -50 }, { x: -100, y: -50 }, { x: 100, y: -50 }, { x: 300, y: -50 },
                    { x: -300, y: -150 }, { x: -100, y: -150 }, { x: 100, y: -150 }, { x: 300, y: -150 }
                ];
            default:
                console.warn(`不支持的弹珠数量: ${count}，使用默认8个位置`);
                return this.getFixedPositions(8);
        }
    }



    private shuffleArray(array: any[]): void {
        for (let i = array.length - 1; i > 0; i--) {
            let j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

    public isAllTargetMarblesStopped(): boolean {
        return this.targetMarbles.every(marble =>
            !marble.isValid || marble.getComponent(RigidBody2D).linearVelocity.length() < 0.1
        );
    }

    public checkeTargetMarblesIn(marble: Node) : boolean {
        let targetAreaSize = this.node.getComponent(UITransform).contentSize;
        const width = targetAreaSize.width;
        const height = targetAreaSize.height;
        if (marble.position.x > -width / 2 && marble.position.x < width / 2 &&
            marble.position.y > -height / 2 && marble.position.y < height / 2
        ) return true;
        return false;
        
    }
    public getOutTarget() {
        const outTargets = []; // 记录没有在目标区域内的节点
        const remainingMarbles = []; // 记录仍在目标区域内的节点

        this.targetMarbles.forEach(marble => {
            if (!this.checkeTargetMarblesIn(marble)) {
                outTargets.push(marble); // 记录没有在目标区域内的节点
                // 移除不在目标区域内的节点
                marble.active = false;
            } else {
                remainingMarbles.push(marble); // 保留仍在区域内的节点
            }
        });

        // 更新目标弹珠数组，只保留未被销毁的节点
        this.targetMarbles = remainingMarbles;
        console.log("this.targetMarbles", this.targetMarbles.length);
        return outTargets; // 返回没有在目标区域内的节点数组
    }

    public checkALLTargetMarblesOut(): boolean {
        // 如果没有目标弹珠，返回true（所有弹珠都被击出）
        // console.log('到达这里了');
        // console.log('this.targetMarbles', this.targetMarbles);
        // console.log('this.targetMarbles', this.targetMarbles.length);
        if (this.targetMarbles.length === 0) {
            return true;
        }
        return false;
    }

    // 返回宽高
    public getTargetAreaSize(): { width: number, height: number } {
        let targetAreaSize = this.node.getComponent(UITransform).contentSize;
        return { width: targetAreaSize.width, height: targetAreaSize.height };
    }

    // 生成障碍物 - 添加到地板节点而不是目标区域
    public generateObstacles(number: number) {
        const obstacleList = [this.obstaclePrefab, this.obstaclePrefab2, this.obstaclePrefab3, this.obstaclePrefab4, this.obstaclePrefab5, this.obstaclePrefab6];
        const fixedObstaclePositions = this.getFixedObstaclePositions(number);

        // 获取地板节点 (目标区域的父节点的父节点)
        const floorNode = this.node.parent;

        for (let i = 0; i < number; i++) {
            const obstacleIndex = i % obstacleList.length;
            const obstacle = instantiate(obstacleList[obstacleIndex]);
            const pos = fixedObstaclePositions[i];

            obstacle.setPosition(pos.x, pos.y, 1100);
            floorNode.addChild(obstacle); // 添加到地板节点
            this.obstacles.push(obstacle);
        }
    }

    // 获取固定障碍物位置 - 在地板范围内，避开目标区域
    private getFixedObstaclePositions(count: number): { x: number, y: number }[] {
        // 地板有效区域: 3000×3000 (去除边界100)
        // 目标区域: 1080×650，居中位置
        // 障碍物放置在目标区域外围和地板其他区域
        const basePositions = [
            // 目标区域左右两侧
            { x: -800, y: 0 }, { x: 800, y: 0 },
            { x: -700, y: 300 }, { x: 700, y: 300 },
            { x: -700, y: -300 }, { x: 700, y: -300 },

            // 目标区域上下方
            { x: 0, y: 600 }, { x: 0, y: -600 },
            { x: -300, y: 500 }, { x: 300, y: 500 },
            { x: -300, y: -500 }, { x: 300, y: -500 },

            // 四个角落区域
            { x: -1000, y: 1000 }, { x: 1000, y: 1000 },
            { x: -1000, y: -1000 }, { x: 1000, y: -1000 },

            // 中距离位置
            { x: -600, y: 600 }, { x: 600, y: 600 },
            { x: -600, y: -600 }, { x: 600, y: -600 },
            { x: 0, y: 800 }, { x: 0, y: -800 }
        ];

        return basePositions.slice(0, count);
    }

    update(deltaTime: number) {

    }
}


