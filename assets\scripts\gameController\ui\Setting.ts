import { _decorator, Button, Component, Node } from 'cc';
import { ButtonAnimationUtils } from '../../Utils/ButtonAnimationUtils';
const { ccclass, property } = _decorator;

@ccclass('Setting')
export class Setting extends Component {

    @property(Node)
    settingUI: Node = null;

    @property(Node)
    cameraNode: Node = null;

    start() {
        // 点击监听
        this.settingUI.active = false;

        // 添加按钮动画效果
        ButtonAnimationUtils.setupButtonInteraction(this.node);

        this.node.getComponent(Button).node.on('click', this.onClick, this);
    }

    onClick() {
        // 显示设置UI
        this.settingUI.setPosition(this.cameraNode.position);
        this.settingUI.active = true;
    }

    update(deltaTime: number) {
        
    }
}


