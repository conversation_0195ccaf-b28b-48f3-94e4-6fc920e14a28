import { _decorator, Component, Label, Node, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, director, tween } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('loading')
export class loading extends Component {

    @property(Label)
    playerName: Label = null;

    @property(Label)
    aiName: Label = null;

    @property(Label)
    PlayerLevel: Label = null;

    @property(Label)
    AiLevel: Label = null;

    @property(ProgressBar)
    progressBar: ProgressBar = null;

 

    private currentProgress: number = 0;
    private progressSlider: Slider = null;

    start() {
        this.node.setPosition(0, 0);
    }

    // 显示加载页面并开始加载游戏场景
    showAndLoadGame(playerName: string, aiName: string, playerLevel: number, aiLevel: number) {
        this.node.active = true;
        this.playerName.string = playerName;
        this.aiName.string = aiName;
        this.PlayerLevel.string = playerLevel.toString();
        this.AiLevel.string = aiLevel.toString();

        this.progressBar.progress = 0;
        this.progressSlider = this.progressBar.node.getComponent(Slider);
        this.currentProgress = 0;

        // 开始加载游戏场景
        this.loadGameScene();
    }

    // 加载游戏场景
    private loadGameScene() {
        director.preloadScene('GameScene',
            (completedCount: number, totalCount: number) => {
                // 更新进度条
                const progress = completedCount / totalCount;
                this.updateProgress(progress);
            },
            (error: Error | null) => {
                if (error) {
                    console.error('加载游戏场景失败:', error);
                    return;
                }
                // 加载完成，跳转到游戏场景
                setTimeout(() => {
                    director.loadScene('GameScene');
                }, 1000);
            }
        );
    }

    // 更新进度条
    private updateProgress(targetProgress: number) {
        const obj = { progress: this.currentProgress };
        tween(obj)
            .to(0.2, { progress: targetProgress })
            .call(() => {
                this.currentProgress = obj.progress;
                // 同时更新ProgressBar和Slider组件
                if (this.progressBar) {
                    this.progressBar.progress = this.currentProgress;
                }
                if (this.progressSlider) {
                    this.progressSlider.progress = this.currentProgress;
                }
            })
            .start();
    }
    

    update() {
        // 实时同步进度到两个组件
        if (this.progressBar) {
            this.progressBar.progress = this.currentProgress;
        }
        if (this.progressSlider) {
            this.progressSlider.progress = this.currentProgress;
        }
    }
}


