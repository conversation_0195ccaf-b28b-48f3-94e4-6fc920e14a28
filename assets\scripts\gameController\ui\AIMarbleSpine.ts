import { _decorator, Component, Node, RigidBody2D, Vec2, sp, director, Collider2D, Contact2DType, IPhysics2DContact, resources } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('AIMarbleSpine')
export class AIMarbleSpine extends Component {

    @property(Node)
    marble: Node = null;

    @property(sp.Skeleton)
    launchEffect: sp.Skeleton = null;

    @property(sp.Skeleton)
    collisionEffect: sp.Skeleton = null;

    // private spineSkeleton: sp.Skeleton = null;
    private effectActive: boolean = false;
    private lastCollisionTime: number = 0; // 上次碰撞时间
    private collisionCooldown: number = 1.0; // 碰撞特效冷却时间（秒）

    // AI随机特效ID
    private randomLaunchEffectId: number = 1;
    private randomCollisionEffectId: number = 101;
    private randomTrailEffectId: number = 201;

    start() {
        // this.spineSkeleton = this.node.getComponent(sp.Skeleton);
        this.node.active = false;
        this.setupCollisionListener();

        // 随机选择AI特效
        this.initRandomEffects();

        // 监听弹珠发射事件
        director.on("aiMove", this.onMarbleLaunched, this);

        director.on('aiStop', this.onAIStop, this);
    }

    // 初始化随机特效
    initRandomEffects() {
        // 随机选择发射特效ID (1-3)
        this.randomLaunchEffectId = Math.floor(Math.random() * 3) + 1;

        // 随机选择碰撞特效ID (101-103)
        this.randomCollisionEffectId = Math.floor(Math.random() * 3) + 101;

        // 随机选择拖尾特效ID (201-203)
        this.randomTrailEffectId = Math.floor(Math.random() * 3) + 201;

        console.log("AI随机特效配置:", {
            发射特效: this.randomLaunchEffectId,
            碰撞特效: this.randomCollisionEffectId,
            拖尾特效: this.randomTrailEffectId
        });

        // 加载随机选择的特效资源
        this.loadRandomEffects();
    }

    // 加载随机特效资源
    loadRandomEffects() {
        this.loadLaunchEffectById(this.randomLaunchEffectId);
        this.loadCollisionEffectById(this.randomCollisionEffectId);
        this.loadTrailEffectById(this.randomTrailEffectId);
    }

    // 根据ID加载发射特效
    loadLaunchEffectById(effectId: number) {
        const resourcePath = "spine/烈焰破风1/发射/001";

        if (!this.launchEffect) {
            console.warn("AI发射特效组件未设置");
            return;
        }

        console.log(`AI加载发射特效: ID=${effectId}`);

        resources.load(resourcePath, sp.SkeletonData, (err, skeletonData) => {
            if (err) {
                console.error(`AI加载发射特效失败: ${resourcePath}`, err);
                return;
            }

            this.launchEffect.skeletonData = skeletonData;
            console.log(`AI发射特效加载成功: ${effectId}`);
        });
    }

    // 根据ID加载碰撞特效
    loadCollisionEffectById(effectId: number) {
        const resourcePath = "spine/烈焰破风1/碰撞/1";

        if (!this.collisionEffect) {
            console.warn("AI碰撞特效组件未设置");
            return;
        }

        console.log(`AI加载碰撞特效: ID=${effectId}`);

        resources.load(resourcePath, sp.SkeletonData, (err, skeletonData) => {
            if (err) {
                console.error(`AI加载碰撞特效失败: ${resourcePath}`, err);
                return;
            }

            this.collisionEffect.skeletonData = skeletonData;
            console.log(`AI碰撞特效加载成功: ${effectId}`);
        });
    }

    // 根据ID加载拖尾特效 (this.node就是拖尾特效)
    loadTrailEffectById(effectId: number) {
        const resourcePath = "spine/烈焰破风1/拖尾/001";

        // 获取this.node上的sp.Skeleton组件
        const trailSkeleton = this.node.getComponent(sp.Skeleton);
        if (!trailSkeleton) {
            console.warn("AI拖尾特效组件未设置");
            return;
        }

        console.log(`AI加载拖尾特效: ID=${effectId}`);

        resources.load(resourcePath, sp.SkeletonData, (err, skeletonData) => {
            if (err) {
                console.error(`AI加载拖尾特效失败: ${resourcePath}`, err);
                return;
            }

            trailSkeleton.skeletonData = skeletonData;
            console.log(`AI拖尾特效加载成功: ${effectId}`);
        });
    }

    // 注册碰撞监听
    setupCollisionListener() {
        const collider = this.marble.getComponent(Collider2D);
        if (collider) {
            collider.on(Contact2DType.BEGIN_CONTACT, this.onCollisionEnter, this);
        }
    }

    onAIStop() {
        this.effectActive = false;
        this.stopTrailEffect(); // 停止拖尾特效
        this.node.active = false;
        console.log("特效禁用 - AI停止");
    }
    
    // 弹珠发射时启用特效
    onMarbleLaunched() {
        this.effectActive = true;
        this.node.active = true;

        // 播放发射特效
        this.playLaunchEffect();

        // 播放拖尾特效
        this.playTrailEffect();

        console.log("特效启用 - AI弹珠发射");
    }

    // 播放AI拖尾特效
    playTrailEffect() {
        const trailSkeleton = this.node.getComponent(sp.Skeleton);
        if (!trailSkeleton) {
            console.warn("AI拖尾特效组件未设置");
            return;
        }

        // 播放拖尾动画，循环播放
        const trackEntry = trailSkeleton.setAnimation(0, "animation", true);
        trackEntry.timeScale = 1.0;

        console.log("AI拖尾特效播放");
    }

    // 停止AI拖尾特效
    stopTrailEffect() {
        const trailSkeleton = this.node.getComponent(sp.Skeleton);
        if (!trailSkeleton) {
            return;
        }

        // 停止动画
        trailSkeleton.clearTracks();
        console.log("AI拖尾特效停止");
    }

    // 播放发射特效
    playLaunchEffect() {
        if (this.launchEffect && this.marble) {
            // 设置发射特效位置
            this.launchEffect.node.active = true;
            this.launchEffect.node.setPosition(this.marble.position);

            // 延迟一帧获取AI弹珠的发射方向（通过速度方向）
            this.scheduleOnce(() => {
                const rigidBody = this.marble.getComponent(RigidBody2D);
                if (rigidBody && rigidBody.linearVelocity.length() > 0.1) {
                    const velocity = rigidBody.linearVelocity;
                    const angle = Math.atan2(velocity.y, velocity.x) * 180 / Math.PI;
                    this.launchEffect.node.setRotationFromEuler(0, 0, angle - 90);
                }
            }, 0.1);

            // 播放发射动画
            const trackEntry = this.launchEffect.setAnimation(0, "animation", false);
            trackEntry.timeScale = 0.3;

            console.log("AI发射特效播放");
        }
    }

    // 碰撞时禁用特效
    onCollisionEnter(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact) {
        // 播放碰撞特效
        this.playCollisionEffect(contact);

        if (this.effectActive) {
            this.effectActive = false;
            this.node.active = false;
            console.log("特效禁用 - 发生碰撞");
        }
    }

    // 播放碰撞特效
    playCollisionEffect(contact: IPhysics2DContact) {
        if (!this.collisionEffect) return;

        // 检查冷却时间
        const currentTime = Date.now() / 1000;
        if (currentTime - this.lastCollisionTime < this.collisionCooldown) {
            return; // 还在冷却中，不播放特效
        }

        // 获取碰撞点位置
        const worldManifold = contact.getWorldManifold();
        if (worldManifold.points.length > 0) {
            // 设置碰撞特效位置为母弹位置
            this.collisionEffect.node.active = true;
            this.collisionEffect.node.setPosition(this.marble.position);

            // 播放碰撞动画
            const trackEntry = this.collisionEffect.setAnimation(0, "animation", false);
            trackEntry.timeScale = 1;

            // 更新上次碰撞时间
            this.lastCollisionTime = currentTime;

            console.log("AI碰撞特效播放");
        }
    }

    followMarble() {
        // 获取刚体
        let rigidbody = this.marble.getComponent(RigidBody2D);

        // 获取刚体速度
        let velocity = rigidbody.linearVelocity;

        // 设置位置跟随弹珠
        this.node.setPosition(this.marble.position);

        // 根据速度方向设置旋转
        if (velocity.length() > 0.1) { // 只在有明显移动时旋转
            const angle = Math.atan2(velocity.y, velocity.x) * 180 / Math.PI - 90;
            this.node.setRotationFromEuler(0, 0, angle);
        }
    }

    onDestroy() {
        // 清理事件监听
        director.off("aiMove", this.onMarbleLaunched, this);
        director.off('aiStop', this.onAIStop, this);

        // 清理碰撞监听
        if (this.marble) {
            const collider = this.marble.getComponent(Collider2D);
            if (collider) {
                collider.off(Contact2DType.BEGIN_CONTACT, this.onCollisionEnter, this);
            }
        }
    }

    update(deltaTime: number) {
        if (this.effectActive) {
            this.followMarble();
        }
    }
}
