2.游戏准备
场地设定: 游戏开始时,系统将在地面上绘制一个矩形目标区域。
圈内弹珠: 玩家和系统玩家(AI)各贡献一定数量的"普通弹珠"(如5-10颗),随机或按预设规则放置在矩形目标区域内。
母弹选择: 玩家和系统玩家(AI)选择一颗"母弹"(主弹珠)进行游戏,母弹可拥有不同外观和属性。
弹射点: 玩家和系统玩家(AI)的"母弹"将从这里开始进行弹射。
3决定出场顺序
通过摇色子决定
4弹射机制
"。
弹珠物理模拟: 游戏精确模拟弹珠的物理碰撞和滚动轨迹。
5回合流程与规则

未击出目标: 玩家或系统玩家(AI)的"母弹"未能击出目标区域内的弹珠,回合结束轮到下一玩家。
击出目标区域内弹珠: 玩家或系统玩家(AI)成功将目标区域内的弹珠击出且母弹未停留在目标区域内,获得这些弹珠,可继续下一次弹射(连击)。
母弹入圈即败: 如果玩家或系统玩家(AI)的"母弹"最终停留在目标区域内,该玩家/系统玩家(AI)立即判定为失败,游戏结束。
6胜利条件
所有目标区域内的弹珠均被击出,拥有最多弹珠的玩家获胜。
7失败条件: 玩家自己的"母弹"进入目标区域，对方获胜。

环境系统
地形影响：不同材质地面影响摩擦系数和弹性
天气效果：风力、湿度等环境因素影响弹珠轨迹

