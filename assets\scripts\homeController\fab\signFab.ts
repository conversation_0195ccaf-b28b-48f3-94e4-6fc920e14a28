import { _decorator, Component, Label, Node, resources, Sprite, SpriteFrame } from 'cc';
const { ccclass, property } = _decorator;

/*
    根据是否签到，显示不同的按钮
    显示天数
*/

@ccclass('signFeb')
export class signFeb extends Component {
    start() {

    }

    init(status: number, day: number) {
        if (status == 1) {
            resources.load("homeUI/签到激活背景/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                this.node.getComponent(Sprite).spriteFrame = spriteFrame;
            })
        }
        else {
            resources.load("homeUI/签到未激活背景/spriteFrame", SpriteFrame, (err, spriteFrame) => {
                this.node.getComponent(Sprite).spriteFrame = spriteFrame;
            })
        }
        this.node.getChildByName("day").getComponent(Label).string = day.toString();
        // console.log('init sign fab')
    }

    update(deltaTime: number) {

    }
}


