import { _decorator, Component, Node } from 'cc';
import { ButtonAnimationUtils } from '../../Utils/ButtonAnimationUtils';
const { ccclass, property } = _decorator;

@ccclass('close')
export class close extends Component {
    start() {
        // 添加按钮动画效果
        ButtonAnimationUtils.setupButtonInteraction(this.node);

        this.node.on('click', this.close, this)
    }

    close() {
        this.node.parent.active = false
    }

    update(deltaTime: number) {
        
    }
}


