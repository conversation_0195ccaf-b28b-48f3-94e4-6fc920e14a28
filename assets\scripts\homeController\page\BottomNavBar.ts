import { _decorator, Component, Node, Toggle, Sprite, resources, SpriteFrame, tween, Vec3 } from 'cc';
import { ButtonAnimationUtils } from '../../Utils/ButtonAnimationUtils';
const { ccclass, property } = _decorator;

/*
    底部导航栏组件 - 管理主页底部Tab切换和页面导航

    主要功能：
    1. Tab切换：通过Toggle组件实现多页面切换
    2. 页面管理：控制不同内容页面的显示/隐藏
    3. 视觉反馈：切换时的底板样式更新和页面动画
    4. 按钮动效：统一的点击动画效果

    使用方式：
    - 挂载到主页Canvas的BottomNavBar节点上
    - 配置contentPages数组：拖入所有内容页面节点
    - 配置navToggles数组：拖入所有底部Tab按钮
    - 默认显示第3个页面（战斗页面）
*/

@ccclass('BottomNavBar')
export class BottomNavBar extends Component {

    @property([Node])
    contentPages: Node[] = []; // 将所有内容页面节点拖拽到这里

    @property([Toggle])
    navToggles: Toggle[] = []; // 将底部导航栏的Toggle按钮拖拽到这里



    onLoad() {
        // 绑定Toggle的点击事件
        this.navToggles.forEach((toggle, index) => {
            toggle.node.on(Toggle.EventType.TOGGLE, (toggle) => {
                this.playButtonClickAnimation(toggle.node);
                this.showPageWithAnimation(index);
                this.updateBottomPlates(index);
            }, this);
        });

        // 初始化显示第3个页面（战斗页面）
        this.showPage(2);
        this.updateBottomPlates(2);

        // 初始化按钮动效
        this.initButtonAnimations();
    }

    showPage(index: number) {
        this.contentPages.forEach((page, i) => {
            page.active = (i === index);
            if (i === index) {
                page.setPosition(0, 0);  // 设置当前显示页面的位置为(0,0)
            }
        });
    }
    
    updateBottomPlates(activeIndex: number) {
        
        this.navToggles.forEach((toggle, i) => {
            const sprite = toggle.node.getComponent(Sprite);
            if (sprite) {
                const imageName = i === activeIndex ? "选中下底板" : "下底板";
                resources.load(`homeUI/${imageName}/spriteFrame`, SpriteFrame, (err, spriteFrame) => {
                    if (!err && spriteFrame) {
                        sprite.spriteFrame = spriteFrame;
                    }
                });
            }
        });
    }

    // 初始化按钮动画效果
    private initButtonAnimations() {
        this.navToggles.forEach((toggle) => {
            // 使用ButtonAnimationUtils设置按钮交互动画
            ButtonAnimationUtils.setupButtonInteraction(toggle.node);
        });
    }

    // 播放按钮点击动画
    private playButtonClickAnimation(buttonNode: Node) {
        // 使用ButtonAnimationUtils播放点击动画
        ButtonAnimationUtils.playClickAnimation(buttonNode);
    }

    // 简单直接：旧页面直接消失，新页面有动画
    private showPageWithAnimation(index: number) {
        // 立即隐藏所有其他页面
        this.contentPages.forEach((page, i) => {
            if (i !== index) {
                page.active = false;
            }
        });

        // 显示新页面并播放动画
        const targetPage = this.contentPages[index];
        targetPage.active = true;
        targetPage.setPosition(0, 0);

        // 新页面快速淡入动画
        targetPage.scale = new Vec3(0.98, 0.98, 1);
        tween(targetPage)
            .to(0.15, { scale: new Vec3(1, 1, 1) }, { easing: 'quadOut' })
            .start();
    }
}