import { _decorator, Component, Node, RigidBody2D, Vec2, UITransform, BoxCollider2D, CircleCollider2D, director, Prefab, instantiate } from 'cc';
const { ccclass, property } = _decorator;
import { TargetArea } from './TargetArea';
import { PlayeMarble } from './PlayeMarble';
import { AiMarble } from './AiMarble';
/*
    提供检测所有弹珠是否停止的方法
    提供给运动的弹珠提供反方向的摩擦力的方法
    生成障碍物
*/

@ccclass('Floor')
export class Floor extends Component {

    @property(TargetArea)
    targetArea: TargetArea = null;

    @property(PlayeMarble)
    playerMarble: PlayeMarble = null;

    @property(AiMarble)
    aiMarble: AiMarble = null;



    @property
    boundarySize: number = 3200;

    @property
    boundaryInset: number = 150; // 边界内缩距离

    @property
    floorFriction: number = 0.3; // 地板摩擦系数

    @property
    weatherFriction: number = 0.8; // 气象摩擦系数

    @property
    floorWallRestriction: number = 0.5; // 地板墙弹性系数

    start() {
        this.setupLargeFloor();
        this.createBoundaries();
    }

    setWeatherFriction(weatherFriction: number) {
        this.weatherFriction = weatherFriction;
    }


    // 设置地板参数函数
    setFloorParam(floorFriction: number, floorWallRestriction: number) {
        this.floorFriction = floorFriction;
        this.floorWallRestriction = floorWallRestriction;
    }
    // 设置地板属性函数
    private setupLargeFloor() {
        // 设置地板大小
        const transform = this.node.getComponent(UITransform);
        transform.setContentSize(this.boundarySize, this.boundarySize);

    }

    private createBoundaries() {
        // 创建四个边界墙，但位置内缩一定距离
        const halfSize = this.boundarySize / 2;
        const wallThickness = 20;
        const inset = this.boundaryInset; // 边界内缩距离

        // 计算内缩后的边界位置
        const topY = halfSize - inset;
        const bottomY = -halfSize + inset;
        const leftX = -halfSize + inset;
        const rightX = halfSize - inset;

        // 计算内缩后的边界长度
        const horizontalWallWidth = this.boundarySize - (2 * inset);
        const verticalWallHeight = this.boundarySize - (2 * inset);

        // 创建内缩的边界墙
        this.createWall("TopWall", 0, topY, horizontalWallWidth, wallThickness);
        this.createWall("BottomWall", 0, bottomY, horizontalWallWidth, wallThickness);
        this.createWall("LeftWall", leftX, 0, wallThickness, verticalWallHeight);
        this.createWall("RightWall", rightX, 0, wallThickness, verticalWallHeight);
    }

    private createWall(name: string, x: number, y: number, width: number, height: number) {
        const wall = new Node(name);
        this.node.addChild(wall);
        wall.setPosition(x, y, 0);

        // 添加UITransform
        const transform = wall.addComponent(UITransform);
        transform.setContentSize(width, height);

        // 添加碰撞器
        const collider = wall.addComponent(BoxCollider2D);
        collider.friction = this.floorFriction;
        collider.restitution = this.floorWallRestriction; // 弹性系数
        collider.sensor = false;
        collider.size.width = width;
        collider.size.height = height;
        collider.apply();

        return wall;
    }

    // 检测所有弹珠是否停止
    public isAllMarbleStop() {
        //先检查母弹是否停止
        if (!this.playerMarble.isStopped()) return false;
        if (!this.aiMarble.isStopped()) return false;
        // 再检查目标弹珠是否停止
        if (!this.targetArea.isAllTargetMarblesStopped()) return false;
        return true;

    }

    // 给运动的弹珠提供反方向的摩擦力的方法
    public applyFrictionToMovingMarbles(marble: Node, force: number) {
        let rb2d = marble.getComponent(RigidBody2D);

        //检测速度是否小于0.1
        if (rb2d.linearVelocity.length() < 0.1) return;

        // 获取其运动方向
        const velocity = rb2d.linearVelocity;

        // 计算反方向的单位向量
        const direction = velocity.clone().normalize().multiplyScalar(-1);

        force = force * this.weatherFriction * this.floorFriction;

        // 应用摩擦力 (力的大小与速度无关，方向与速度相反)
        rb2d.applyForceToCenter(direction.multiplyScalar(force), true);
        // console.log("applyFrictionToMovingMarbles");
    }

    update(deltaTime: number) {
        //判断是否有运动的弹珠，如果有，则给其施加摩擦力
        if (!this.isAllMarbleStop()) {
            this.applyFrictionToMovingMarbles(this.playerMarble.node, 150);
            this.applyFrictionToMovingMarbles(this.aiMarble.node, 150);
            //
            const targetMarbles = this.targetArea.targetMarbles;
            for (const targetMarble of targetMarbles) {
                if (targetMarble && targetMarble.isValid) {
                    this.applyFrictionToMovingMarbles(targetMarble, 150);
                }
            }
        }
        // 通知管理器
        else {
            director.emit('AllMarbleStop');
        }

    }
}


