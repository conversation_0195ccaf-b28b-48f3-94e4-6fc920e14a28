import { director, JsonAsset, resources } from 'cc';
import { User } from '../Entity/UserEntity';

export class DataManager {
    private static _instance: DataManager = null;

    public static getInstance(): DataManager {
        if (!this._instance) {
            this._instance = new DataManager();
        }
        return this._instance;
    }

    public currentPlayer: string = null;
    public User: User = null;

    // 游戏数据
    public playerMarblesHit: number = 0;
    public aiMarblesHit: number = 0;
    public currentLevel: 'primary' | 'middle' | 'senior' = 'primary';
    public playerReward: number = 0;
    public playerFinalReward: number = 0;

    // 场次配置
    private levels = {
        primary: { base: 4, fee: 0, bonus: 2, loseRate: 0 },      // 初级场（新手场）
        middle: { base: 5, fee: 10, bonus: 3, loseRate: 0.6 },   // 中级场（进阶场）
        senior: { base: 10, fee: 20, bonus: 6, loseRate: 0.6 }   // 高级场（大师场）
    };

    public initialize_home() {
        // 监听用户初始化
        director.on('userInit', this.userInit, this);
        // 监听装备卡片
        director.on('equipSkin', this.equipSkin, this);
        director.on('equipLaunchEffect', this.equipLaunchEffect, this);
        director.on('equipCollisionEffect', this.equipCollisionEffect, this);
        director.on('equipTrailEffect', this.equipTrailEffect, this);
    }
    public initialize_game() {
        //监听游戏顺序阶段
        director.on("gameInit", this.gameinit, this);
        director.on("orderState", this.onOrderState, this);
    }

    equipSkin(skinId: number) {
        if (!this.User) {
            console.error("用户数据不存在");
            return;
        }

        // 验证用户是否拥有该皮肤
        if (!this.User.hasSkin(skinId)) {
            console.warn(`用户未拥有皮肤: ${skinId}`);
            return;
        }

        // 装备皮肤
        this.User.currentSkin = skinId;

        // 保存用户数据
        if (this.User.save()) {
            console.log(`装备皮肤成功: ${skinId}`);

            // 发送事件通知UI刷新装备标识
            director.emit('equipmentChanged', {
                type: 'skin',
                id: skinId
            });
        } else {
            console.error("保存用户数据失败");
        }
    }

    equipLaunchEffect(launchEffectId: number) {
        if (!this.User) {
            console.error("用户数据不存在");
            return;
        }

        // 验证用户是否拥有该特效
        if (!this.User.hasLaunchEffect(launchEffectId)) {
            console.warn(`用户未拥有发射特效: ${launchEffectId}`);
            return;
        }

        // 装备特效
        this.User.currentLaunchEffect = launchEffectId;

        // 保存用户数据
        if (this.User.save()) {
            console.log(`装备发射特效成功: ${launchEffectId}`);

            // 发送事件通知UI刷新装备标识
            director.emit('equipmentChanged', {
                type: 'launchEffect',
                id: launchEffectId
            });
        } else {
            console.error("保存用户数据失败");
        }
    }

    equipCollisionEffect(collisionEffectId: number) {
        if (!this.User) {
            console.error("用户数据不存在");
            return;
        }

        // 验证用户是否拥有该特效
        if (!this.User.hasCollisionEffect(collisionEffectId)) {
            console.warn(`用户未拥有碰撞特效: ${collisionEffectId}`);
            return;
        }

        // 装备特效
        this.User.currentCollisionEffect = collisionEffectId;

        // 保存用户数据
        if (this.User.save()) {
            console.log(`装备碰撞特效成功: ${collisionEffectId}`);

            // 发送事件通知UI刷新装备标识
            director.emit('equipmentChanged', {
                type: 'collisionEffect',
                id: collisionEffectId
            });
        } else {
            console.error("保存用户数据失败");
        }
    }

    equipTrailEffect(trailEffectId: number) {
        if (!this.User) {
            console.error("用户数据不存在");
            return;
        }

        // 验证用户是否拥有该特效
        if (!this.User.hasTrailEffect(trailEffectId)) {
            console.warn(`用户未拥有拖尾特效: ${trailEffectId}`);
            return;
        }

        // 装备特效
        this.User.currentTrailEffect = trailEffectId;

        // 保存用户数据
        if (this.User.save()) {
            console.log(`装备拖尾特效成功: ${trailEffectId}`);

            // 发送事件通知UI刷新装备标识
            director.emit('equipmentChanged', {
                type: 'trailEffect',
                id: trailEffectId
            });
        } else {
            console.error("保存用户数据失败");
        }
    }

    // 进入主页
    userInit(userId: number) {
        console.log("开始加载用户数据", userId);

        // 优先从本地存储加载用户数据
        const localUser = User.load(userId);
        if (localUser) {
            this.User = localUser;
            console.log("从本地存储加载用户成功:", this.User.username);
            console.log("用户拥有的发射特效:", this.User.ownedLaunchEffects);
            console.log("用户拥有的碰撞特效:", this.User.ownedCollisionEffects);
            console.log("用户拥有的拖尾特效:", this.User.ownedTrailEffects);

            director.emit("userInited");
            return;
        }
        else {
            console.log("本地存储没有用户数据，从json加载");
            // 从json加载用户数据
            resources.load("data/users", JsonAsset, (err, asset) => {
                if (err) {
                    console.error("加载用户数据失败:", err);
                    return;
                }
                const userData = asset.json.users.find(user => user.userId === userId);
                this.User = new User(userData);
                console.log("从json加载用户成功:", this.User.username);
                director.emit("userInited");
            });
        }

    }
    // 初始化数据
    gameinit() {
        if (!this.User) {
            console.error("用户数据不存在，无法开始游戏");
            return;
        }

        const level = this.levels[this.currentLevel];
        const totalCost = level.base + level.fee;

        // 检查用户弹珠是否足够
        if (this.User.marbleCount < totalCost) {
            console.error(`弹珠不足！需要${totalCost}颗，当前${this.User.marbleCount}颗`);
            return;
        }

        // 扣除入场费和基础投入
        this.User.deductMarbles(totalCost);

        console.log(`游戏开始 - 扣除${totalCost}颗弹珠，余额：${this.User.marbleCount}颗`);

        // 计算目标弹珠数量并传递
        const targetMarbleCount = level.base * 2;
        director.emit("gameInit_", targetMarbleCount, this.User.username, this.User.level);
        // director.emit("gameInit_", 8);
    }


    onOrderState(playerNumber: Number, aiNumber: Number, Firster: string) {
        this.currentPlayer = Firster;
        console.log(`当前玩家: ${this.currentPlayer}`);
    }

    // 切换玩家
    switchPlayer() {
        if (this.currentPlayer === "player") {
            this.currentPlayer = "ai";
        } else {
            this.currentPlayer = "player";
        }
        console.log(`切换到: ${this.currentPlayer}`);
    }

    // 比较谁击出的多
    compareMarbles() {
        if (this.playerMarblesHit > this.aiMarblesHit) {
            return "player";
        } else if (this.playerMarblesHit < this.aiMarblesHit) {
            return "ai";
        } else {
            return "tie";
        }
    }

    // 记录击出弹珠
    addHitMarbles(player: string, count: number) {
        if (player === "player") {
            this.playerMarblesHit += count;
        } else {
            this.aiMarblesHit += count;
        }
    }

    // 计算玩家弹珠收益
    calculateFianlReward(result: string) {
        const level = this.levels[this.currentLevel];
        const totalCost = level.base + level.fee;

        let reward = this.playerMarblesHit; // 战利品

        if (result === "win") {
            reward += level.fee + level.bonus; // 胜利：返还入场费+奖励

        } else if (result === "tie") {
            reward += level.fee; // 平局：返还入场费
        } else {
            reward += Math.floor(level.fee * level.loseRate); // 失败：部分返还
        }

        this.playerReward = reward;
        this.playerFinalReward = reward - totalCost;
    }

    // 结算游戏
    settleGame(result: string): boolean {
        if (!this.User) return false;

        // 计算收益
        this.calculateFianlReward(result);

        // 使用实体类方法更新弹珠（自动保存）
        this.User.addMarbles(this.playerReward);

        // 根据场次和结果计算经验值
        const expGained = this.calculateExpReward(result);
        const expResult = this.User.addExp(expGained);

        // 增加游戏次数并更新新手任务状态
        this.User.gameCount++;
        this.User.updateBeginnerTaskStatus();
        this.User.save();

        // 输出结算信息
        let logMessage = `结算完成：收获${this.playerReward > 0 ? '+' : ''}${this.playerReward}颗弹珠，获得${expGained}经验值，余额：${this.User.marbleCount}颗，游戏次数：${this.User.gameCount}`;
        
        if (expResult.levelUp) {
            logMessage += `，恭喜升级到${expResult.newLevel}级！`;
        }
        
        console.log(logMessage);

        return true;
    }

    // 计算经验值奖励
    private calculateExpReward(result: string): number {
        const baseExp = {
            primary: { win: 20, tie: 10, lose: 5 },    // 初级场经验值
            middle: { win: 40, tie: 20, lose: 10 },    // 中级场经验值  
            senior: { win: 80, tie: 40, lose: 20 }     // 高级场经验值
        };

        const levelExp = baseExp[this.currentLevel];
        return levelExp[result] || 0;
    }

    // 设置场次
    setLevel(level: 'primary' | 'middle' | 'senior') {
        this.currentLevel = level;
        console.log(`设置场次: ${level === 'primary' ? '初级场' : level === 'middle' ? '中级场' : '高级场'}`);
    }

    // 重置游戏数据
    resetGameStats() {
        this.playerMarblesHit = 0;
        this.aiMarblesHit = 0;
    }

    // 用户数据写回
    saveUser() {

    }

    // 清理事件监听
    destroy() {
        director.off('userInit', this.userInit, this);
        director.off("gameInit", this.gameinit, this);
        director.off("orderState", this.onOrderState, this);
        director.off('equipSkin', this.equipSkin, this);
        director.off('equipLaunchEffect', this.equipLaunchEffect, this);
        director.off('equipCollisionEffect', this.equipCollisionEffect, this);
        director.off('equipTrailEffect', this.equipTrailEffect, this);
    }

}
