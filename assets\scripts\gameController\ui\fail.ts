import { _decorator, Component, director, Node } from 'cc';
import { ButtonAnimationUtils } from '../../Utils/ButtonAnimationUtils';
const { ccclass, property } = _decorator;

@ccclass('fail')
export class fail extends Component {
    start() {
        // 添加按钮动画效果
        ButtonAnimationUtils.setupButtonInteraction(this.node);

        this.node.on('click', this.onclick, this);
    }

    onclick() {
        director.emit('choosefail');
    }

    update(deltaTime: number) {

    }
}


