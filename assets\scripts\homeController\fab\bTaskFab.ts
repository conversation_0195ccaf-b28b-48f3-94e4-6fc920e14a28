import { _decorator, Component, Label, Node, resources, Sprite, SpriteFrame } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('bTaskFab')
export class bTaskFab extends Component {

    @property(Node)
    gotNode: Node = null;

    @property(Node)
    activeNode: Node = null;

    @property(Label)
    requireLabel: Label = null;

    @property(Node)
    noticeNode: Node = null;

    start() {

    }
    set(completed: boolean, claimed: boolean, require: string) {
        if (completed) {
            resources.load('homeUI/task1/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                this.node.getComponent(Sprite).spriteFrame = spriteFrame;
            })
            this.activeNode.active = false;
            this.noticeNode.active = true;
            this.gotNode.active = false;
            if (claimed) {
                this.gotNode.active = true;
                this.noticeNode.active = false;
            }
        }
        else {
            resources.load('homeUI/task0/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                this.node.getComponent(Sprite).spriteFrame = spriteFrame;
            })
            this.activeNode.active = true;
            this.gotNode.active = false;
            this.noticeNode.active = false;
        }
        this.requireLabel.string = require;
    }



    update(deltaTime: number) {

    }
}


