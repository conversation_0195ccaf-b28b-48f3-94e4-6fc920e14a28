import { _decorator, Component, Node, Vec2, v2, UITransform, EventTouch, Camera, find, v3, Sprite } from 'cc';
const { ccclass, property } = _decorator;

/*
    实现动态显示瞄准线，提供瞄准线方向的方法（使用精灵节点）
*/

@ccclass('AimLine')
export class AimLine extends Component {
    @property(Node)
    private slingshotOrigin: Node = null;

    @property(Node)
    private lineSprite: Node = null; // 瞄准线精灵节点

    @property
    private lineLength: number = 150;

    private currentDirection: Vec2 = v2(0, 1);
    private isAiming: boolean = false;

    start() {
        this.init();
    }

    //初始化函数
    public init() {
        this.node.active = true;

        //初始化瞄准线
        this.drawAimLine();
    }

    //隐藏函数
    public hide() {
        this.node.active = false;

    }

    private onTouchEnd(event: EventTouch) {
        this.isAiming = false;
    }

    public drawAimLine() {
        if (!this.lineSprite || !this.slingshotOrigin) return;

        const uiTransform = this.node.getComponent(UITransform);
        const lineSpriteTransform = this.lineSprite.getComponent(UITransform);
        if (!uiTransform || !lineSpriteTransform) return;

        const slingshotOriginWorldPos = this.slingshotOrigin.worldPosition;
        const originInLocalSpace = uiTransform.convertToNodeSpaceAR(slingshotOriginWorldPos);

        // 设置瞄准线精灵的位置（起点在弹弓位置）
        const lineEndPos = v2(
            originInLocalSpace.x + this.currentDirection.x * this.lineLength,
            originInLocalSpace.y + this.currentDirection.y * this.lineLength
        );
        
        // 瞄准线精灵的中心点位置
        const lineCenterPos = v2(
            (originInLocalSpace.x + lineEndPos.x) / 2,
            (originInLocalSpace.y + lineEndPos.y) / 2
        );

        this.lineSprite.setPosition(v3(lineCenterPos.x, lineCenterPos.y, 0));

        // 设置瞄准线的长度
        lineSpriteTransform.height = this.lineLength;

        // 计算并设置瞄准线的角度
        const angle = Math.atan2(this.currentDirection.y, this.currentDirection.x);
        const angleInDegrees = angle * 180 / Math.PI;
        // 因为精灵默认是垂直向上的，所以需要减去90度
        this.lineSprite.setRotationFromEuler(0, 0, angleInDegrees - 90);
    }

    // 获取当前瞄准方向
    public getDirection(): Vec2 {
        return this.currentDirection;
    }

    // 直接设置瞄准方向（用于拖拽控制）
    public setDirection(direction: Vec2): void {
        this.currentDirection = direction.normalize();
        this.drawAimLine();
    }
}