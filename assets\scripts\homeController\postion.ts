import { _decorator, Component, Node, Prefab, ScrollView, resources, JsonAsset, instantiate } from 'cc';
import { postionFab } from './fab/postionFab';
const { ccclass, property } = _decorator;

/*
    位置/地区系统组件 - 管理地区选择和位置信息展示

    主要功能：
    1. 地区展示：显示可选择的地区/位置列表
    2. 滚动查看：支持滚动视图浏览所有地区选项
    3. 数据加载：从配置文件加载地区数据
    4. 选择交互：支持地区选择和确认功能

    使用方式：
    - 通过rankPage的位置按钮激活显示
    - 自动加载地区配置数据
    - 支持滚动查看和选择地区
*/

@ccclass('postion')
export class postion extends Component {

    @property(Prefab)
    postionPrefab: Prefab = null;

    @property(ScrollView)
    scrollView: ScrollView = null;  

    @property(Node)
    closeBtn: Node = null;

    start() {
        this.generateitem();
        this.closeBtn.on('click', this.close, this);
    }

    generateitem() {
        // 加载全国省份数据
        resources.load('data/provinces', JsonAsset, (err, asset) => {
            if (!err) {
                const provincesData = asset.json;
                this.showProvinceList(provincesData.provinces);
            } else {
                console.error('加载省份数据失败:', err);
            }
        });
    }

    // 显示省份列表
    showProvinceList(provinces: any[]) {
        if (!this.postionPrefab || !this.scrollView) return;

        // 清空滚动视图内容
        this.scrollView.content.removeAllChildren();

        // 为每个省份创建一个项目
        for (let i = 0; i < provinces.length; i++) {
            const province = provinces[i];
            const provinceNode = instantiate(this.postionPrefab);
            const provinceComponent = provinceNode.getComponent(postionFab);

            if (provinceComponent) {
                provinceComponent.setCityName(province.name);
            }
            this.scrollView.content.addChild(provinceNode);
        }
    }
    close() {
        this.node.active = false;
    }    
    update(deltaTime: number) {
        // console.log(this.scrollView.content.children);
    }
}


