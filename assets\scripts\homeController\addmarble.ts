import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('addmarble')
export class addmarble extends Component {
    start() {
        this.node.getChildByName('back').on('click', this.back, this);
        this.node.getChildByName('watch').on('click', this.watch, this);
        this.node.getChildByName('close').on('click', this.close, this);
    }

    back() {
        this.node.active = false;
    }

    watch() {
        console.log('watch');
    }

    close() {
        this.node.active = false;
    }

    update(deltaTime: number) {
        
    }
}