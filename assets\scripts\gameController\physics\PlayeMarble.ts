import { _decorator, Component, Node, RigidBody2D, Vec2, Vec3, UITransform, resources, SpriteFrame, Sprite, Collider2D, Contact2DType, IPhysics2DContact, director } from 'cc';
const { ccclass, property } = _decorator;

/*
    实现母弹的发射以及运动过程
    提供检测是否停止运动的方法
    投骰子方法
    控制玩家回合初始化与销毁的的方法
    
*/
@ccclass('PlayeMarble')
export class PlayeMarble extends Component {


    @property(Vec2)
    private launchDirection: Vec2 = new Vec2(0, -1300);

    @property
    private shootForce: number = 200;

    @property
    marbleFriction: number; //弹珠摩擦系数

    @property
    marbleRestitution: number; //弹珠弹性系数


    start() {
        // 设置弹珠初始位置
        this.node.setPosition(new Vec3(this.launchDirection.x, this.launchDirection.y, 0));
        // this.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onBeginContact, this);
    }
    // onBeginContact() {
    //     director.emit('beginContact');  
    // }

    // 设置玩家弹珠参数
    setPlayerMarbleParam(marbleSpritename: string) {
        marbleSpritename = "gameUI/" + marbleSpritename + "/spriteFrame";
        resources.load(marbleSpritename, SpriteFrame, (err, spriteFrame) => {
            this.node.getComponent(Sprite).spriteFrame = spriteFrame;
            // console.log("加载AI弹珠图片", err);
        })
    }

    //初始化函数
    public init() {
        this.node.active = true;
    }

    public hide() {
        this.node.active = false;
    }

    //投骰子
    public rollDice() {
        return Math.floor(Math.random() * 6) + 1;
    }

    // 获取发射力量
    public getShootForce(): number {
        return this.shootForce;
    }

    // 实现发射母弹，接收参数：发射方向，力量大小，初始力量
    public launch(direction: Vec2, power: number, shootForce: number) {
        console.log(`PlayeMarble.launch - 方向: (${direction.x.toFixed(2)}, ${direction.y.toFixed(2)}), 力度: ${power.toFixed(2)}, 发射力量: ${shootForce}`);

        const rigidBody = this.node.getComponent(RigidBody2D);

        const force = direction.clone().multiplyScalar(power * shootForce);

        console.log(`PlayeMarble.launch - 计算得到的力: (${force.x.toFixed(2)}, ${force.y.toFixed(2)})`);

        // 施加冲量让物体弹出
        rigidBody.applyLinearImpulse(force, Vec2.ZERO, true);

        // 打印质量和初始速度
        console.log(`PlayeMarble.launch - 质量: ${rigidBody.getMass()}, 初始速度: (${rigidBody.linearVelocity.x.toFixed(2)}, ${rigidBody.linearVelocity.y.toFixed(2)})`);

    }

    //实现检测是否停止运动的方法
    public isStopped(): boolean {
        const rigidBody = this.node.getComponent(RigidBody2D);

        const velocity = rigidBody.linearVelocity;

        return velocity.length() < 0.1;
    }


    update(deltaTime: number) {
        //验证阻尼公式

    }
}


