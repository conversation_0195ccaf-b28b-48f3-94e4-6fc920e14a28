[{"__type__": "cc.Prefab", "_name": "Obstacle", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Obstacle", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": -1000.77, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a54LO1PbJEcJpFZ7bXmvDL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c603657c-376e-469d-9d28-7aa4f75fbe4e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aG9eGP/RNioRuiTdLppDJ"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 0, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5eCaZPNO1CpZn/40wkF5z1"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": 10, "y": 50}, {"__type__": "cc.Vec2", "x": 7, "y": 47.77777777777777}, {"__type__": "cc.Vec2", "x": -1, "y": 47.77777777777777}, {"__type__": "cc.Vec2", "x": -2, "y": 46.29629629629629}, {"__type__": "cc.Vec2", "x": -6.5, "y": 45.55555555555556}, {"__type__": "cc.Vec2", "x": -10.5, "y": 43.33333333333333}, {"__type__": "cc.Vec2", "x": -11, "y": 41.1111111111111}, {"__type__": "cc.Vec2", "x": -14.5, "y": 35.925925925925924}, {"__type__": "cc.Vec2", "x": -14.5, "y": 34.44444444444444}, {"__type__": "cc.Vec2", "x": -16, "y": 33.703703703703695}, {"__type__": "cc.Vec2", "x": -17, "y": 30.740740740740733}, {"__type__": "cc.Vec2", "x": -19, "y": 28.51851851851852}, {"__type__": "cc.Vec2", "x": -19, "y": 27.037037037037038}, {"__type__": "cc.Vec2", "x": -20.5, "y": 25.555555555555557}, {"__type__": "cc.Vec2", "x": -22.5, "y": 19.62962962962962}, {"__type__": "cc.Vec2", "x": -30.9, "y": -1.6}, {"__type__": "cc.Vec2", "x": -15.5, "y": -31.48148148148148}, {"__type__": "cc.Vec2", "x": -12, "y": -36.66666666666667}, {"__type__": "cc.Vec2", "x": -12, "y": -38.14814814814815}, {"__type__": "cc.Vec2", "x": -10.5, "y": -38.888888888888886}, {"__type__": "cc.Vec2", "x": -9, "y": -42.592592592592595}, {"__type__": "cc.Vec2", "x": -8, "y": -42.592592592592595}, {"__type__": "cc.Vec2", "x": -7.5, "y": -45.55555555555556}, {"__type__": "cc.Vec2", "x": -6, "y": -46.2962962962963}, {"__type__": "cc.Vec2", "x": -6, "y": -47.77777777777778}, {"__type__": "cc.Vec2", "x": -4, "y": -50}, {"__type__": "cc.Vec2", "x": 7, "y": -50}, {"__type__": "cc.Vec2", "x": 9, "y": -47.77777777777778}, {"__type__": "cc.Vec2", "x": 11.5, "y": -47.03703703703704}, {"__type__": "cc.Vec2", "x": 33, "y": -43.333333333333336}, {"__type__": "cc.Vec2", "x": 35, "y": -41.111111111111114}, {"__type__": "cc.Vec2", "x": 36.5, "y": -36.66666666666667}, {"__type__": "cc.Vec2", "x": 39.5, "y": -35.18518518518519}, {"__type__": "cc.Vec2", "x": 40, "y": -32.96296296296296}, {"__type__": "cc.Vec2", "x": 43.5, "y": -27.77777777777778}, {"__type__": "cc.Vec2", "x": 44.5, "y": -19.62962962962963}, {"__type__": "cc.Vec2", "x": 45.5, "y": -18.14814814814815}, {"__type__": "cc.Vec2", "x": 46, "y": -7.777777777777779}, {"__type__": "cc.Vec2", "x": 47, "y": 0.3703703703703667}, {"__type__": "cc.Vec2", "x": 48, "y": 1.8518518518518476}, {"__type__": "cc.Vec2", "x": 48.5, "y": 11.481481481481481}, {"__type__": "cc.Vec2", "x": 50, "y": 15.185185185185176}, {"__type__": "cc.Vec2", "x": 50, "y": 23.33333333333333}, {"__type__": "cc.Vec2", "x": 47.5, "y": 27.77777777777777}, {"__type__": "cc.Vec2", "x": 47.5, "y": 29.259259259259252}, {"__type__": "cc.Vec2", "x": 45, "y": 30.740740740740733}, {"__type__": "cc.Vec2", "x": 44, "y": 32.96296296296296}, {"__type__": "cc.Vec2", "x": 40, "y": 35.185185185185176}, {"__type__": "cc.Vec2", "x": 38.5, "y": 37.407407407407405}, {"__type__": "cc.Vec2", "x": 34.5, "y": 38.888888888888886}, {"__type__": "cc.Vec2", "x": 34, "y": 40.37037037037037}, {"__type__": "cc.Vec2", "x": 31, "y": 41.85185185185185}, {"__type__": "cc.Vec2", "x": 29.5, "y": 44.07407407407406}, {"__type__": "cc.Vec2", "x": 25.5, "y": 45.55555555555556}, {"__type__": "cc.Vec2", "x": 21.5, "y": 50}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6SAFCFhVFzrpcq53Lun6W"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10VKGpxlZGsYVlfA5GVU/t", "instance": null, "targetOverrides": null}]