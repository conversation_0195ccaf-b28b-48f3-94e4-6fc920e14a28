import { _decorator, Component, director, Node } from 'cc';
import { ButtonAnimationUtils } from '../../Utils/ButtonAnimationUtils';
const { ccclass, property } = _decorator;

@ccclass('jixu')
export class jixu extends Component {


    @property(Node)
    settingUI: Node = null;

    start() {
        // 添加按钮动画效果
        ButtonAnimationUtils.setupButtonInteraction(this.node);

        this.node.on('click', this.onClick, this);
    }

    onClick() {
        this.settingUI.active = false;
    }

    update(deltaTime: number) {

    }
}


