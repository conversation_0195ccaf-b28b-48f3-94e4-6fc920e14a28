import { _decorator, Component, Node, Prefab, ProgressBar, instantiate, Button, Label, director } from 'cc';
import { DataManager } from '../Managers/DataManager';
import { User } from '../Entity/UserEntity';
import { bTaskFab } from './fab/bTaskFab';
import { signFeb } from './fab/signFab';
import { resultTip } from './fab/resultTip';
import { ButtonAnimationUtils } from '../Utils/ButtonAnimationUtils';

const { ccclass, property } = _decorator;

/*
    新手闯关礼福利活动界面 - 管理新手礼包和福利奖励系统

    主要功能：
    1. 新手礼包展示：显示新手可领取的礼包内容
    2. 闯关奖励：根据玩家进度提供相应奖励
    3. 福利活动：管理各种限时福利和活动奖励
    4. 领取逻辑：处理奖励领取和状态更新

    使用方式：
    - 自动检测玩家资格和可领取奖励
    - 提供关闭和领取交互功能
*/
@ccclass('beginPresent')
export class beginPresent extends Component {

    @property(ProgressBar)
    progressBar: ProgressBar = null;

    @property(Prefab)
    signFab: Prefab = null;

    @property(Prefab)
    taskFab: Prefab = null;

    @property(Node)
    signContainer: Node = null;

    @property(Node)
    taskContainer: Node = null;

    @property(Button)
    recvieButton: Button = null;

    @property(Button)
    closeButton: Button = null;

    @property(Prefab)
    resultTipPrefab: Prefab = null;

    @property(Label)
    restTimeLabel: Label = null;

    private dataManager: DataManager = null;
    private currentUser: User = null;

    // 新手任务配置
    private taskConfigs = [
        { taskId: 1, reward: '弹珠+1' },
        { taskId: 3, reward: '皮肤体验卡' },
        { taskId: 5, reward: '弹珠+1' },
        { taskId: 8, reward: '特效体验卡' },
        { taskId: 10, reward: '弹珠+2' },
        { taskId: 20, reward: '永久四星弹珠皮肤' }
    ];

    start() {
        this.dataManager = DataManager.getInstance();
        this.currentUser = this.dataManager.User;
        this.setupSignFab();
        this.setuptaskFab();
        this.updateProgressBar();
        this.setupReceiveButton();
        this.updateRestTime();
        this.closeButton.node.on('click', this.onCloseClick, this);
    }


    setupSignFab() {
        if (!this.signContainer || !this.signFab || !this.currentUser) return;

        // 清空容器
        this.signContainer.removeAllChildren();

        // 为每个任务创建signFab，day对应任务序号
        for (let i = 0; i < this.taskConfigs.length; i++) {
            const taskStatus = this.currentUser.beginnerTaskStatus[i];

            const signNode = instantiate(this.signFab);
            const signComponent = signNode.getComponent(signFeb);

            if (signComponent) {
                // status: 1表示已完成，0表示未完成
                const status = taskStatus.completed ? 1 : 0;
                // day对应任务序号 (1, 2, 3, 4, 5, 6)
                const day = i + 1;

                signComponent.init(status, day);

            }

            this.signContainer.addChild(signNode);

        }
    }

    setuptaskFab() {
        if (!this.taskContainer || !this.taskFab || !this.currentUser) return;

        // 清空容器
        this.taskContainer.removeAllChildren();

        // 创建任务项
        for (let i = 0; i < this.taskConfigs.length; i++) {
            const config = this.taskConfigs[i];
            const taskStatus = this.currentUser.beginnerTaskStatus[i];

            const taskNode = instantiate(this.taskFab);
            const taskComponent = taskNode.getComponent(bTaskFab);

            if (taskComponent) {
                const isCompleted = taskStatus.completed;
                const isClaimed = taskStatus.claimed;
                const requireText = `完成${config.taskId}局`;

                taskComponent.set(isCompleted, isClaimed, requireText);
                // console.log(`i:${i},isClaimed: ${isClaimed}, canClaim: ${canClaim}, isCompleted: ${isCompleted}`);
            }

            this.taskContainer.addChild(taskNode);
        }
    }

    // 设置一键领取按钮
    setupReceiveButton() {
        if (!this.recvieButton) return;

        // 添加按钮动画效果
        ButtonAnimationUtils.setupButtonInteraction(this.recvieButton.node);

        this.recvieButton.node.on('click', this.onReceiveAllClick, this);
        this.updateReceiveButtonState();
    }

    // 一键领取所有可领取的奖励
    onReceiveAllClick() {
        if (!this.currentUser) return;

        let hasReceived = false;
        let allRewards: Array<{ name: string, count: number }> = [];

        // 遍历所有任务，领取可领取的奖励
        for (let taskStatus of this.currentUser.beginnerTaskStatus) {
            if (taskStatus.completed && !taskStatus.claimed) {
                const result = this.currentUser.claimBeginnerTaskReward(taskStatus.taskId);
                if (result.success) {
                    console.log(`领取奖励成功: ${result.reward.name}`);
                    allRewards.push({ name: result.reward.name, count: result.reward.count || 1 });
                    hasReceived = true;
                }
            }
        }

        if (hasReceived) {
            // 显示恭喜获得弹窗
            this.showResultTip(allRewards);

            // 刷新UI
            this.setupSignFab();
            this.setuptaskFab();
            this.updateProgressBar();
            this.updateReceiveButtonState();

            // 更新TopBar显示
            this.updateTopBarDisplay();
        } else {
            console.log("没有可领取的奖励");
        }
    }

    // 显示恭喜获得弹窗
    showResultTip(rewards: Array<{ name: string, count: number }>) {
        if (!this.resultTipPrefab) return;

        const resultTipNode = instantiate(this.resultTipPrefab);
        const resultTipComponent = resultTipNode.getComponent(resultTip);
        if (resultTipComponent) {
            resultTipComponent.set(rewards);
        }

        this.node.addChild(resultTipNode);
    }

    // 更新一键领取按钮状态
    updateReceiveButtonState() {
        if (!this.recvieButton || !this.currentUser) return;

        // 检查是否有可领取的奖励
        const hasClaimableReward = this.currentUser.beginnerTaskStatus.some(
            task => task.completed && !task.claimed
        );

        this.recvieButton.interactable = hasClaimableReward;
    }

    updateProgressBar() {
        if (!this.progressBar || !this.currentUser) return;

        const totalTasks = this.taskConfigs.length;
        const claimedTasks = this.currentUser.beginnerTaskStatus.filter(task => task.claimed).length;
        const progress = claimedTasks / totalTasks;
        this.progressBar.progress = progress;
    }

    onCloseClick() {
        this.node.active = false;
    }

    updateRestTime() {
        // 更新活动剩余时间
        if (this.restTimeLabel) {
            const remaining = new Date(this.currentUser.createTime).getTime() + 7 * 24 * 60 * 60 * 1000 - Date.now();
            if (remaining > 0) {
                const days = Math.floor(remaining / (24 * 60 * 60 * 1000));
                const hours = Math.floor((remaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
                const minutes = Math.floor((remaining % (60 * 60 * 1000)) / (60 * 1000));
                const seconds = Math.floor((remaining % (60 * 1000)) / 1000);
                this.restTimeLabel.string = `${days}天${hours}小时${minutes}分${seconds}秒`;
            } else {
                this.restTimeLabel.string = "活动已结束";
            }
        }
    }
    /**
     * 更新TopBar的显示（弹珠数量、等级、经验等）
     */
    private updateTopBarDisplay() {
        director.emit('updateTopBarAllInfo');
    }

    update(_deltaTime: number) {
        this.updateRestTime();
    }
}


