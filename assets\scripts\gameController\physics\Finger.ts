import { _decorator, Component, Node, EventTouch, Vec2, Vec3, v3, v2, director } from 'cc';
const { ccclass, property } = _decorator;
import { PowerBar } from './PowerBar';
import { AimLine } from './AimLine';
import { PlayeMarble } from './PlayeMarble';

/*
    拖拽控制发射功能
    位置总是在方向线反方向
    通过拖拽手指来控制瞄准方向和力度
*/

@ccclass('Finger')
export class Finger extends Component {

    @property
    private maxDragDistance: number = 200;

    @property
    private minDragDistance: number = 20;

    @property(PowerBar)
    private powerBar: PowerBar = null;

    @property(AimLine)
    private aimLine: AimLine = null;

    @property(PlayeMarble)
    private playerMarble: PlayeMarble = null;

    private isDragging: boolean = false;
    private dragStartPos: Vec2 = new Vec2();
    private marblePos: Vec3 = new Vec3();

    start() {
        this.init();
    }

    //初始化函数
    public init() {
        this.node.active = true;

        // 获取弹珠位置
        this.marblePos = this.playerMarble.node.position.clone();

        // 获取瞄准线当前方向
        const aimDirection = this.aimLine.getDirection();

        // 设置手指初始位置（在瞄准线反方向，距离弹珠一定距离）
        const fingerDistance = 50; // 手指距离弹珠的距离
        const fingerPos = this.marblePos.clone().subtract(
            v3(aimDirection.x * fingerDistance, aimDirection.y * fingerDistance, 0)
        );
        this.node.setPosition(fingerPos);

        // 注册拖拽事件
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    public hide() {
        this.node.active = false;

        // 移除拖拽事件
        this.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.off(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    // 拖拽事件处理
    private onTouchStart(event: EventTouch) {
        this.isDragging = true;
        this.dragStartPos = event.getLocation();
        // 记录弹珠当前位置
        this.marblePos = this.playerMarble.node.position.clone();
        console.log('开始拖拽手指');
    }

    private onTouchMove(event: EventTouch) {
        if (!this.isDragging) return;

        const currentTouchPos = event.getLocation();
        const dragVector = this.calculateDragVector(this.dragStartPos, currentTouchPos);

        //调试
        // console.log(`拖拽距离: ${dragVector.length()}`);

        // 更新力度条
        this.powerBar.setPower(this.calculatePower(dragVector.length()));

        // 更新手指位置（视觉反馈）
        this.updateFingerPosition(dragVector);

        // 更新瞄准线方向
        this.aimLine.setDirection(dragVector.normalize());
    }

    private onTouchEnd(event: EventTouch) {
        if (!this.isDragging) return;

        this.isDragging = false;

        const currentTouchPos = event.getLocation();
        const dragVector = this.calculateDragVector(this.dragStartPos, currentTouchPos);

        // 检查是否达到最小拖拽距离
        if (dragVector.length() < this.minDragDistance) {
            console.log('拖拽距离太短，取消发射');
            this.resetPosition();
            this.aimLine.drawAimLine();
            return;
        }

        // 发射弹珠
        this.launchWithDrag(dragVector);

        console.log('松手发射弹珠');
    }

    private calculateDragVector(startPos: Vec2, currentPos: Vec2): Vec2 {
        // 计算拖拽向量（从当前位置指向起始位置，因为要反向发射）
        const dragVector = v2(startPos.x - currentPos.x, startPos.y - currentPos.y);

        // 限制最大拖拽距离
        if (dragVector.length() > this.maxDragDistance) {
            dragVector.normalize().multiplyScalar(this.maxDragDistance);
        }

        return dragVector;
    }

    private calculatePower(dragDistance: number): number {
        // 将拖拽距离从 [minDragDistance, maxDragDistance] 映射到 [0, 1]
        if (dragDistance < this.minDragDistance) {
            return 0;
        }

        const effectiveDistance = dragDistance - this.minDragDistance;
        const maxEffectiveDistance = this.maxDragDistance - this.minDragDistance;
        const power = Math.min(effectiveDistance / maxEffectiveDistance, 1.0);

        // console.log(`拖拽距离: ${dragDistance.toFixed(1)}, 最小: ${this.minDragDistance}, 最大: ${this.maxDragDistance}, 力度: ${power.toFixed(2)}`);

        return power;
    }

    private updateFingerPosition(dragVector: Vec2) {
        // 根据拖拽距离计算手指应该在瞄准线反方向的位置
        const dragDistance = dragVector.length();
        const baseDistance = 100; // 基础距离
        const maxDistance = 200; // 最大距离

        // 根据拖拽距离调整手指距离弹珠的距离
        const fingerDistance = baseDistance + (dragDistance / this.maxDragDistance) * maxDistance;

        // 获取当前瞄准方向（拖拽方向）
        const aimDirection = dragVector.normalize();

        // 设置手指位置在瞄准线反方向
        const fingerPos = this.marblePos.clone().subtract(
            v3(aimDirection.x * fingerDistance, aimDirection.y * fingerDistance, 0)
        );
        this.node.setPosition(fingerPos);
    }

    private resetPosition() {
        // 重置手指位置到瞄准线反方向的初始位置
        const aimDirection = this.aimLine.getDirection();
        const fingerDistance = 100;
        const fingerPos = this.marblePos.clone().subtract(
            v3(aimDirection.x * fingerDistance, aimDirection.y * fingerDistance, 0)
        );
        this.node.setPosition(fingerPos);
    }

    private launchWithDrag(dragVector: Vec2) {
        // 计算发射方向
        const direction = dragVector.normalize();

        // 从力度条获取当前力度值
        const power = this.powerBar.getCurrentPower();

        console.log(`发射 - 方向: (${direction.x.toFixed(2)}, ${direction.y.toFixed(2)}), 力度条力度: ${power.toFixed(2)}`);

        // 重置手指位置
        this.resetPosition();

        // 发射弹珠
        this.playerMarble.launch(direction, power, this.playerMarble.getShootForce());

        // 通知游戏管理器
        director.emit('marble-launched', this.playerMarble);


    }

    update(deltaTime: number) {

    }
}


