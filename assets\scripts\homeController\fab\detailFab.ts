import { _decorator, Component, director, Label, Node, resources, sp, Sprite, SpriteFrame, tween, UITransform, v3 } from 'cc';
import { DataManager } from '../../Managers/DataManager';
const { ccclass, property } = _decorator;

/*
    卡片点击后的详情展示弹窗
*/

@ccclass('detailFeb')
export class detailFeb extends Component {

    @property(sp.Skeleton)
    skeleton: sp.Skeleton = null;

    @property(Node)
    sprite: Node = null;

    @property(Node)
    spriteAnimation: Node = null;

    public data: any = null;


    start() {
        this.node.getChildByName('close').on('click', this.close, this);
        this.node.getChildByName('equip').on('click', this.equip, this);
    }

    init(data: any) {
        this.data = data;
        const name = data.skinName || data.effectName;

        this.node.getChildByName('name').getChildByName('name').getComponent(Label).string = name;
        this.node.getChildByName('desc').getChildByName('desc').getComponent(Label).string = data.description;
        if (data.rarity == 3) this.node.getChildByName('star').getChildByName('star3').active = true;
        else if (data.rarity == 4) this.node.getChildByName('star').getChildByName('star4').active = true;
        else if (data.rarity == 5) this.node.getChildByName('star').getChildByName('star5').active = true;

        if (data.skinId) {
            resources.load(`skins/${data.skinId}/spriteFrame`, SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    this.sprite.getComponent(Sprite).spriteFrame = spriteFrame;
                    this.spriteAnimation.getComponent(Sprite).spriteFrame = spriteFrame;
                    this.startDropAndRotateAnimation();
                }
                // console.log(err);
            });
            //

        } else if (data.launchEffectId) {
            resources.load(`spine/${data.launchEffectId}/发射/show/spriteFrame`, SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    this.sprite.getComponent(Sprite).spriteFrame = spriteFrame;
                    this.sprite.getComponent(UITransform).setContentSize(200, 300);
                }
                // console.log(err);
            });
            resources.load(`spine/${data.launchEffectId}/发射/001`, sp.SkeletonData, (err, skeletonData) => {
                if (err) {
                    console.error(`加载发射特效失败: `, err);
                    return;
                }
                this.skeleton.skeletonData = skeletonData;
                this.skeleton.node.setPosition(0, 300);
                this.skeleton.setAnimation(0, 'animation', true);
            });
        }

        else if (data.collisionEffectId) {
            resources.load(`spine/${data.collisionEffectId}/碰撞/show/spriteFrame`, SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    this.sprite.getComponent(Sprite).spriteFrame = spriteFrame;
                }
                // console.log(err);
            });
            resources.load(`spine/${data.collisionEffectId}/碰撞/1`, sp.SkeletonData, (err, skeletonData) => {
                if (err) {
                    console.error(`加载碰撞特效失败: `, err);
                    return;
                }
                this.skeleton.skeletonData = skeletonData;
                // this.skeleton.node.setPosition(0, 300);
                this.skeleton.setAnimation(0, 'animation', true);
            })
        }

        else if (data.trailEffectId) {
            resources.load(`spine/${data.trailEffectId}/拖尾/show/spriteFrame`, SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    this.sprite.getComponent(Sprite).spriteFrame = spriteFrame;
                    this.sprite.getComponent(UITransform).setContentSize(200, 300);
                }
                // console.log(err);
            });
            resources.load(`spine/${data.trailEffectId}/拖尾/001`, sp.SkeletonData, (err, skeletonData) => {
                if (err) {
                    console.error(`加载拖尾特效失败: `, err);
                    return;
                }
                this.skeleton.skeletonData = skeletonData;
                this.skeleton.node.setPosition(0, 300);
                this.skeleton.setAnimation(0, 'animation', true);
            })
        }

    }
    startDropAndRotateAnimation() {
        const initialPosition = this.spriteAnimation.position.clone();

        // 设置开始位置为上方
        this.spriteAnimation.setPosition(initialPosition.x, initialPosition.y + 200, 0);
        this.spriteAnimation.angle = 0;

        // 第一次掉落动画
        tween(this.spriteAnimation)
            .to(1.0, { position: v3(initialPosition.x, initialPosition.y, 0) }, { easing: 'quadOut' }) // 掉落
            .call(() => {
                // 掉落完成后，开始无限循环旋转
                const rotateAnimation = tween(this.spriteAnimation)
                    .by(4.0, { angle: 1080 }, { easing: 'linear' }) // 旋转4秒
                    .call(() => {
                        this.spriteAnimation.angle = 0; // 重置角度
                    });

                // 无限循环旋转
                tween(this.spriteAnimation)
                    .repeatForever(rotateAnimation)
                    .start();
            })
            .start();
    }
    equip() {
        const dataManager = DataManager.getInstance();
        const user = dataManager.User;

        if (!user || !this.data) {
            console.error("用户数据或装备数据不存在");
            return;
        }

        // 处理皮肤装备
        if (this.data.skinId) {
            const skinId = this.data.skinId;

            // 验证用户是否拥有该皮肤
            if (!user.hasSkin(skinId)) {
                console.warn(`用户未拥有皮肤: ${skinId}`);
                // 可以显示提示信息
                return;
            }

            // 装备皮肤
            director.emit('equipSkin', skinId);

        } else if (this.data.launchEffectId) {
            const launchEffectId = this.data.launchEffectId;

            // 验证用户是否拥有该发射特效
            if (!user.hasLaunchEffect(launchEffectId)) {
                console.warn(`用户未拥有发射特效: ${launchEffectId}`);
                return;
            }

            // 装备发射特效
            director.emit('equipLaunchEffect', launchEffectId);

        } else if (this.data.collisionEffectId) {
            const collisionEffectId = this.data.collisionEffectId;

            // 验证用户是否拥有该碰撞特效
            if (!user.hasCollisionEffect(collisionEffectId)) {
                console.warn(`用户未拥有碰撞特效: ${collisionEffectId}`);
                return;
            }

            // 装备碰撞特效
            director.emit('equipCollisionEffect', collisionEffectId);

        } else if (this.data.trailEffectId) {
            const trailEffectId = this.data.trailEffectId;

            // 验证用户是否拥有该拖尾特效
            if (!user.hasTrailEffect(trailEffectId)) {
                console.warn(`用户未拥有拖尾特效: ${trailEffectId}`);
                return;
            }

            // 装备拖尾特效
            director.emit('equipTrailEffect', trailEffectId);
        }

        // 装备成功后关闭弹窗
        this.close();
    }

    close() {
        this.node.destroy();
    }

    update(deltaTime: number) {

    }
}


