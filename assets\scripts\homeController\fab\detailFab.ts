import { _decorator, Component, director, Label, Node } from 'cc';
import { DataManager } from '../../Managers/DataManager';
const { ccclass, property } = _decorator;

/*
    卡片点击后的详情展示弹窗
*/

@ccclass('detailFeb')
export class detailFeb extends Component {
    public data: any = null;

    start() {
        this.node.getChildByName('close').on('click', this.close, this);
        this.node.getChildByName('equip').on('click', this.equip, this);
    }

    init(data: any) {
        this.data = data;
        const name = data.skinName || data.effectName;

        this.node.getChildByName('name').getChildByName('name').getComponent(Label).string = name;
        this.node.getChildByName('desc').getChildByName('desc').getComponent(Label).string = data.description;
        if (data.rarity == 3) this.node.getChildByName('star').getChildByName('star3').active = true;
        else if (data.rarity == 4) this.node.getChildByName('star').getChildByName('star4').active = true;
        else if (data.rarity == 5) this.node.getChildByName('star').getChildByName('star5').active = true;
    }

    equip() {
        const dataManager = DataManager.getInstance();
        const user = dataManager.User;

        if (!user || !this.data) {
            console.error("用户数据或装备数据不存在");
            return;
        }

        // 处理皮肤装备
        if (this.data.skinId) {
            const skinId = this.data.skinId;

            // 验证用户是否拥有该皮肤
            if (!user.hasSkin(skinId)) {
                console.warn(`用户未拥有皮肤: ${skinId}`);
                // 可以显示提示信息
                return;
            }

            // 装备皮肤
            director.emit('equipSkin', skinId);

        } else if (this.data.launchEffectId) {
            const launchEffectId = this.data.launchEffectId;

            // 验证用户是否拥有该发射特效
            if (!user.hasLaunchEffect(launchEffectId)) {
                console.warn(`用户未拥有发射特效: ${launchEffectId}`);
                return;
            }

            // 装备发射特效
            director.emit('equipLaunchEffect', launchEffectId);

        } else if (this.data.collisionEffectId) {
            const collisionEffectId = this.data.collisionEffectId;

            // 验证用户是否拥有该碰撞特效
            if (!user.hasCollisionEffect(collisionEffectId)) {
                console.warn(`用户未拥有碰撞特效: ${collisionEffectId}`);
                return;
            }

            // 装备碰撞特效
            director.emit('equipCollisionEffect', collisionEffectId);

        } else if (this.data.trailEffectId) {
            const trailEffectId = this.data.trailEffectId;

            // 验证用户是否拥有该拖尾特效
            if (!user.hasTrailEffect(trailEffectId)) {
                console.warn(`用户未拥有拖尾特效: ${trailEffectId}`);
                return;
            }

            // 装备拖尾特效
            director.emit('equipTrailEffect', trailEffectId);
        }

        // 装备成功后关闭弹窗
        this.close();
    }

    close() {
        this.node.destroy();
    }

    update(deltaTime: number) {
        
    }
}


