{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "f5d11f8c-b427-4ab2-b0a9-2e13ac4294df", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "f5d11f8c-b427-4ab2-b0a9-2e13ac4294df@6c48a", "displayName": "荣耀黄金", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "f5d11f8c-b427-4ab2-b0a9-2e13ac4294df", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "f5d11f8c-b427-4ab2-b0a9-2e13ac4294df@f9941", "displayName": "荣耀黄金", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 101, "height": 64, "rawWidth": 101, "rawHeight": 64, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-50.5, -32, 0, 50.5, -32, 0, -50.5, 32, 0, 50.5, 32, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 64, 101, 64, 0, 0, 101, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-50.5, -32, 0], "maxPos": [50.5, 32, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "f5d11f8c-b427-4ab2-b0a9-2e13ac4294df@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "f5d11f8c-b427-4ab2-b0a9-2e13ac4294df@6c48a"}}