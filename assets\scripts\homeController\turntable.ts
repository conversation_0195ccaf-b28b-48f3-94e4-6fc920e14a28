import { _decorator, Button, Component, Node, Prefab, ProgressBar, resources, JsonAsset, instantiate, Vec3, tween, Label } from 'cc';
import { turnFeb } from './fab/turnFab';
import { DataManager } from '../Managers/DataManager';
import { User } from '../Entity/UserEntity';
import { resultTip } from './fab/resultTip';
const { ccclass, property } = _decorator;
/*
    转盘抽奖系统组件 - 管理幸运转盘抽奖功能

    主要功能：
    1. 转盘抽奖：8个格子的圆形转盘，包含各种奖励
    2. 奖励配置：限时4、5星皮肤特效和永久3、4星道具
    3. 抽奖方式：支持观看视频和消耗抽奖券两种方式
    4. 重复处理：已拥有限时道具延长时间，永久道具转换碎片
    5. 动画效果：转盘旋转动画和中奖结果展示

    使用方式：
    - 通过BattlePage的转盘按钮激活显示
    - 自动加载转盘奖励配置数据
    - 支持跳过动画和正常抽奖模式
*/

@ccclass('turntable')
export class turntable extends Component {

    @property(Prefab)
    turntablePrefab: Prefab = null;

    // 以此节点为中心，生成8个随机奖品fab
    @property(Node)
    downturntableNode: Node = null;

    @property(Button)
    byvideoButton: Button = null;

    @property(Button)
    byticketButton: Button = null;

    // 抽取次数进度
    @property(ProgressBar)
    progressBar: ProgressBar = null;

    @property(Button)
    closeButton: Button = null;

    // 已抽取次数
    @property(Label)
    timesLabel: Label = null;

    // 活动剩余时间
    @property(Label)
    restTimeLabel: Label = null;

    @property(Prefab)
    resultTipPrefab: Prefab = null;

    @property(Button)
    skipButton: Button = null;

    private dataManager: DataManager = null;
    private currentUser: User = null;

    private allSkinsData: any[] = [];
    private allEffectsData: any[] = [];
    private turntableItems: any[] = []; // 转盘上的8个奖品
    private turntableNodes: Node[] = []; // 转盘节点数组

    private isSpinning: boolean = false; // 是否正在转动
    private timeUpdateCounter: number = 0; // 时间更新计数器

    private isSkipAniming: boolean = false; // 是否勾选跳过动画
    start() {
        // this.node.active = false;
        this.dataManager = DataManager.getInstance();
        this.currentUser = this.dataManager.User;
        this.loadTurntableData();
        this.setupButtons();
        this.updateUI();
    }

    async loadTurntableData() {
        try {
            // 加载皮肤和三种特效数据
            const [skinsAsset, launchEffectsAsset, collisionEffectsAsset, trailEffectsAsset] = await Promise.all([
                this.loadJsonAsset('data/skins'),
                this.loadJsonAsset('data/launchEffects'),
                this.loadJsonAsset('data/collisionEffects'),
                this.loadJsonAsset('data/trailEffects')
            ]);

            this.allSkinsData = (skinsAsset as JsonAsset).json.skins;
            const allLaunchEffects = (launchEffectsAsset as JsonAsset).json.launchEffects;
            const allCollisionEffects = (collisionEffectsAsset as JsonAsset).json.collisionEffects;
            const allTrailEffects = (trailEffectsAsset as JsonAsset).json.trailEffects;

            // 合并所有特效数据
            this.allEffectsData = [...allLaunchEffects, ...allCollisionEffects, ...allTrailEffects];

            this.generateTurntableItems();
            this.createTurntableNodes();

        } catch (error) {
            console.error("加载转盘数据失败:", error);
        }
    }

    loadJsonAsset(path: string): Promise<JsonAsset> {
        return new Promise((resolve, reject) => {
            resources.load(path, JsonAsset, (err, asset) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(asset);
                }
            });
        });
    }

    // 生成转盘奖品（8个格子）
    generateTurntableItems() {
        this.turntableItems = [];

        // 3个3星（永久）
        const threeStarItems = [...this.allSkinsData, ...this.allEffectsData].filter(item => item.rarity === 3);
        for (let i = 0; i < 3; i++) {
            const item = threeStarItems[Math.floor(Math.random() * threeStarItems.length)];
            this.turntableItems.push({ ...item, isTimed: false });
        }

        // 2个4星（永久）
        const fourStarItems = [...this.allSkinsData, ...this.allEffectsData].filter(item => item.rarity === 4);
        for (let i = 0; i < 2; i++) {
            const item = fourStarItems[Math.floor(Math.random() * fourStarItems.length)];
            this.turntableItems.push({ ...item, isTimed: false });
        }

        // 1个非限定5星（永久）
        const nonLimitedFiveStars = [...this.allSkinsData, ...this.allEffectsData].filter(item => item.rarity === 5 && !item.isLimited);
        if (nonLimitedFiveStars.length > 0) {
            const item = nonLimitedFiveStars[Math.floor(Math.random() * nonLimitedFiveStars.length)];
            this.turntableItems.push({ ...item, isTimed: false });
        }

        // 2个限定5星（限时）
        const limitedFiveStars = [...this.allSkinsData, ...this.allEffectsData].filter(item => item.rarity === 5 && item.isLimited);
        for (let i = 0; i < 2 && limitedFiveStars.length > 0; i++) {
            const item = limitedFiveStars[Math.floor(Math.random() * limitedFiveStars.length)];
            this.turntableItems.push({ ...item, isTimed: true });
        }

        // 打乱顺序
        for (let i = this.turntableItems.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.turntableItems[i], this.turntableItems[j]] = [this.turntableItems[j], this.turntableItems[i]];
        }
    }

    // 创建转盘节点
    createTurntableNodes() {
        if (!this.downturntableNode || !this.turntablePrefab) return;

        this.turntableNodes.forEach(node => node.destroy());
        this.turntableNodes = [];

        for (let i = 0; i < 8; i++) {
            const angle = i * 45 * Math.PI / 180;
            const itemNode = instantiate(this.turntablePrefab);
            itemNode.setPosition(new Vec3(Math.cos(angle) * 300, Math.sin(angle) * 300, 0)); // 圆形布局，半径200

            const component = itemNode.getComponent(turnFeb);
            if (component) component.setItemData(this.turntableItems[i]);

            this.downturntableNode.addChild(itemNode);
            this.turntableNodes.push(itemNode);
        }
    }

    // 设置按钮事件
    setupButtons() {
        this.byvideoButton?.node.on('click', this.onVideoSpin, this);
        this.byticketButton?.node.on('click', this.onTicketSpin, this);
        this.closeButton?.node.on('click', this.onClose, this);
        this.skipButton.node.on('click', this.onSkip, this);
    }
    onSkip() {
        if (this.isSkipAniming) {
            this.isSkipAniming = false;
            this.skipButton.node.getChildByName('yes').active = false;

        }
        else {
            this.isSkipAniming = true;
            this.skipButton.node.getChildByName('yes').active = true;
        }
    }

    // 更新UI显示
    updateUI() {
        // 更新进度条和次数
        if (this.progressBar) this.progressBar.progress = this.currentUser.turntableDrawCount / 70;
        if (this.timesLabel) this.timesLabel.string = `${this.currentUser.turntableDrawCount}/70`;

        // 更新活动剩余时间
        if (this.restTimeLabel) {
            const remaining = new Date(this.currentUser.createTime).getTime() + 7 * 24 * 60 * 60 * 1000 - Date.now();
            if (remaining > 0) {
                const days = Math.floor(remaining / (24 * 60 * 60 * 1000));
                const hours = Math.floor((remaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
                const minutes = Math.floor((remaining % (60 * 60 * 1000)) / (60 * 1000));
                const seconds = Math.floor((remaining % (60 * 1000)) / 1000);
                this.restTimeLabel.string = `活动剩余时间：${days}天${hours}小时${minutes}分钟${seconds}秒`;
            } else {
                this.restTimeLabel.string = "活动已结束";
            }
        }
    }

    // 关闭转盘
    onClose() {
        this.node.active = false;
    }

    // 刷新转盘奖品（手动调用）
    refreshTurntable() {
        this.generateTurntableItems();
        this.createTurntableNodes();
        this.updateUI();
    }

    // 观看视频转盘
    onVideoSpin() {
        if (!this.isSpinning) this.spinTurntable();
    }

    // 使用抽奖券转盘
    onTicketSpin() {
        if (this.isSpinning || !this.currentUser.deductLotteryTickets(1)) return;
        this.spinTurntable();
    }

    // 转盘旋转
    spinTurntable() {
        this.isSpinning = true;
        this.currentUser.turntableDrawCount++;

        const winItem = this.selectWinItem();
        const winIndex = this.turntableItems.indexOf(winItem);

        // 将当前角度标准化到0-360度范围
        const currentAngle = (this.downturntableNode.angle || 0) % 360;
        const targetAngle = -(winIndex * 45) + 90;

        if (this.isSkipAniming) {
            // 跳过动画：直接设置到目标角度
            this.downturntableNode.angle = targetAngle;
            this.handleWinItem(winItem);
            this.isSpinning = false;
        } else {
            // 正常动画：旋转到目标位置
            const baseRotation = 360 * 4;

            // 计算最短路径到目标角度
            let angleDiff = targetAngle - currentAngle;
            if (angleDiff > 180) angleDiff -= 360;
            if (angleDiff < -180) angleDiff += 360;

            const finalRotation = currentAngle + baseRotation + angleDiff;

            tween(this.downturntableNode)
                .to(3, { angle: finalRotation }, { easing: 'quadOut' })
                .call(() => {
                    // 旋转完成后重置角度到标准范围
                    this.downturntableNode.angle = this.downturntableNode.angle % 360;
                    this.handleWinItem(winItem);
                    this.isSpinning = false;
                })
                .start();
        }
    }

    // 选择中奖物品（概率控制+保底）
    selectWinItem(): any {
        // 70抽保底：必出5星
        if (this.currentUser.turntableDrawCount >= 70) {
            const fiveStarItems = this.turntableItems.filter(item => item.rarity === 5);
            this.currentUser.turntableDrawCount = 0; // 重置保底
            return fiveStarItems[Math.floor(Math.random() * fiveStarItems.length)];
        }

        // // 前50次不能抽到5星
        // if (this.currentUser.turntableDrawCount < 50) {
        //     const nonFiveStarItems = this.turntableItems.filter(item => item.rarity !== 5);
        //     const weights = nonFiveStarItems.map(item => {
        //         if (item.rarity === 4) return 8; // 4星权重8
        //         return 20; // 3星权重20
        //     });

        //     const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
        //     let random = Math.random() * totalWeight;

        //     for (let i = 0; i < weights.length; i++) {
        //         random -= weights[i];
        //         if (random <= 0) return nonFiveStarItems[i];
        //     }
        // }

        // 正常概率：根据稀有度权重选择
        const weights = this.turntableItems.map(item => {
            if (item.rarity === 5) return item.isLimited ? 1 : 2; // 限定5星权重1，非限定5星权重2
            if (item.rarity === 4) return 8; // 4星权重8
            return 20; // 3星权重20
        });

        const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
        let random = Math.random() * totalWeight;

        for (let i = 0; i < weights.length; i++) {
            random -= weights[i];
            if (random <= 0) {
                console.log(`抽到${this.turntableItems[i].skinName || this.turntableItems[i].effectName}`);
                return this.turntableItems[i];
            }
        }

    }

    // 处理获得的奖品
    handleWinItem(item: any) {
        const itemId = item.skinId || item.effectId;
        const isEffect = !!item.effectId;
        let isOwned = false;
        if (isEffect) {
            // 根据特效ID范围判断特效类型
            if (itemId >= 1 && itemId <= 50) {
                isOwned = this.currentUser.hasLaunchEffect(itemId);
            } else if (itemId >= 101 && itemId <= 150) {
                isOwned = this.currentUser.hasCollisionEffect(itemId);
            } else if (itemId >= 201 && itemId <= 250) {
                isOwned = this.currentUser.hasTrailEffect(itemId);
            }
        } else {
            isOwned = this.currentUser.hasSkin(itemId);
        }
        const itemName = item.skinName || item.effectName;
        const prefix = item.isTimed ? "[限时]" : "";

        if (item.isTimed) {
            // 限时物品（限定5星） - 添加或延长7天 + 奖励1个限定碎片
            if (isEffect) {
                // 根据特效ID范围判断特效类型
                if (itemId >= 1 && itemId <= 50) {
                    this.currentUser.addTimedLaunchEffect(itemId, 7);
                } else if (itemId >= 101 && itemId <= 150) {
                    this.currentUser.addTimedCollisionEffect(itemId, 7);
                } else if (itemId >= 201 && itemId <= 250) {
                    this.currentUser.addTimedTrailEffect(itemId, 7);
                }
            } else {
                this.currentUser.addTimedSkin(itemId, 7);
            }
            this.currentUser.addLimitedPieces(1);
            console.log(`获得限时${isEffect ? '特效' : '皮肤'}并奖励1个限定碎片`);
            // 显示限时物品获得弹窗 - 两个奖励
            this.showResultTip([
                {name: `${prefix}${itemName}`, count: 1},
                {name: "限定碎片", count: 1}
            ]);
        } else if (isOwned) {
            // 已拥有永久物品 - 转换为普通碎片
            const pieces = item.rarity === 5 ? 20 : item.rarity === 4 ? 10 : 0;
            this.currentUser.addPieces(pieces);
            // 显示碎片获得弹窗
            if (pieces > 0) {
                this.showResultTip("普通碎片", pieces);
            }
        } else {
            // 新获得永久物品
            if (isEffect) {
                // 根据特效ID范围判断特效类型
                if (itemId >= 1 && itemId <= 50) {
                    this.currentUser.ownedLaunchEffects.push(itemId);
                } else if (itemId >= 101 && itemId <= 150) {
                    this.currentUser.ownedCollisionEffects.push(itemId);
                } else if (itemId >= 201 && itemId <= 250) {
                    this.currentUser.ownedTrailEffects.push(itemId);
                }
            } else {
                this.currentUser.ownedSkins.push(itemId);
            }
            // 显示新物品获得弹窗
            this.showResultTip(`${prefix}${itemName}`, 1);
        }

        // 重置保底（如果抽到5星）
        if (item.rarity === 5) {
            this.currentUser.turntableDrawCount = 0;
        }

        this.currentUser.save();
        this.updateUI();
    }

    // 显示恭喜获得弹窗
    showResultTip(rewards: Array<{name: string, count: number}> | string, count?: number) {
        if (!this.resultTipPrefab) return;

        const resultTipNode = instantiate(this.resultTipPrefab);
        const resultTipComponent = resultTipNode.getComponent(resultTip);
        if (resultTipComponent) {
            if (Array.isArray(rewards)) {
                resultTipComponent.set(rewards);
            } else {
                resultTipComponent.set([{name: rewards, count: count}]);
            }
        }

        this.node.addChild(resultTipNode);
    }

    update(deltaTime: number) {
        // 每秒更新一次时间显示
        this.timeUpdateCounter += deltaTime;
        if (this.timeUpdateCounter >= 1.0) {
            this.timeUpdateCounter = 0;
            this.updateUI();
        }
    }
}
