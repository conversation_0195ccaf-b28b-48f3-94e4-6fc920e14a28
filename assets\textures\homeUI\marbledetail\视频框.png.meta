{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "ea3db94c-71ab-41ef-a5c4-114dc4db8df7", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "ea3db94c-71ab-41ef-a5c4-114dc4db8df7@6c48a", "displayName": "视频框", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "ea3db94c-71ab-41ef-a5c4-114dc4db8df7", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "ea3db94c-71ab-41ef-a5c4-114dc4db8df7@f9941", "displayName": "视频框", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 712, "height": 637, "rawWidth": 712, "rawHeight": 637, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-356, -318.5, 0, 356, -318.5, 0, -356, 318.5, 0, 356, 318.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 637, 712, 637, 0, 0, 712, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-356, -318.5, 0], "maxPos": [356, 318.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "ea3db94c-71ab-41ef-a5c4-114dc4db8df7@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "ea3db94c-71ab-41ef-a5c4-114dc4db8df7@6c48a"}}