import { _decorator, Component, Node, director, Label } from 'cc';
import { DataManager } from '../../Managers/DataManager';
const { ccclass, property } = _decorator;

/*
    控制顶部栏 - 负责显示和管理主页顶部用户信息区域
    
    功能包括：
    1. 显示用户基本信息（用户名、等级、经验值）
    2. 显示弹珠数量
    3. 发起用户初始化事件并更新UI
    4. 提供事件驱动的UI更新机制
    
    主要方法：
    - userInited(): 用户初始化完成后更新所有UI显示
    - updateMarbleCount(): 更新弹珠数量显示
    - updateUserLevel(): 更新用户等级显示  
    - updateUserExp(): 更新经验值显示
    - updateAllUserInfo(): 更新所有用户信息显示
    
    事件系统：
    - 'updateTopBarMarbleCount': 更新弹珠数量
    - 'updateTopBarUserLevel': 更新用户等级
    - 'updateTopBarUserExp': 更新经验值
    - 'updateTopBarAllInfo': 更新所有信息
*/
@ccclass('TopBar')
export class TopBar extends Component {

    @property(Node)
    addNode: Node = null;

    private dataManager: DataManager = null;

    onLoad() {
        // 获取数据管理器实例
        this.dataManager = DataManager.getInstance();
        // 初始化主页数据管理
        this.dataManager.initialize_home();
        
        // 监听UI更新事件
        this.setupEventListeners();
    }

    start() {
        // 监听用户初始化完成事件
        director.on('userInited', this.userInited, this);

        // 初始化用户数据 - 原HomeManager功能
        const userId = 1;
        director.emit("userInit", userId);
        console.log("开始用户初始化");
        this.addNode.on('click',this.onAddClick, this); 
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners() {
        director.on('updateTopBarMarbleCount', this.updateMarbleCount, this);
        director.on('updateTopBarUserLevel', this.updateUserLevel, this);
        director.on('updateTopBarUserExp', this.updateUserExp, this);
        director.on('updateTopBarAllInfo', this.updateAllUserInfo, this);
    }

    /**
     * 用户初始化完成后更新UI显示 - 原HomeUIManager功能
     */
    userInited() {
        console.log("用户初始化完成");

        // 更新用户名
        const nameNode = this.node.getChildByName('Grade')?.getChildByName('name');
        if (nameNode) {
            const nameLabel = nameNode.getComponent(Label);
            if (nameLabel) {
                nameLabel.string = this.dataManager.User.username;
            }
        }

        // 更新经验值
        const expNode = this.node.getChildByName('Grade')?.getChildByName('exp');
        if (expNode) {
            const expLabel = expNode.getComponent(Label);
            if (expLabel) {
                expLabel.string = `${this.dataManager.User.exp}/400`;
            }
        }

        // 更新等级
        const levelNode = this.node.getChildByName('Grade')?.getChildByName('level');
        if (levelNode) {
            const levelLabel = levelNode.getComponent(Label);
            if (levelLabel) {
                levelLabel.string = this.dataManager.User.level.toString();
            }
        }

        // 更新弹珠数量
        const marbleCountNode = this.node.getChildByName('MarbleCount')?.getChildByName('count');
        if (marbleCountNode) {
            const marbleCountLabel = marbleCountNode.getComponent(Label);
            if (marbleCountLabel) {
                marbleCountLabel.string = this.dataManager.User.marbleCount.toString();
            }
        }
    }

    /**
     * 更新弹珠数量显示 
     */
    updateMarbleCount() {
        const marbleCountNode = this.node.getChildByName('MarbleCount')?.getChildByName('count');
        if (marbleCountNode && this.dataManager?.User) {
            const marbleCountLabel = marbleCountNode.getComponent(Label);
            if (marbleCountLabel) {
                marbleCountLabel.string = this.dataManager.User.marbleCount.toString();
            }
        }
    }

    /**
     * 更新用户等级显示 
     */
    updateUserLevel() {
        const levelNode = this.node.getChildByName('Grade')?.getChildByName('level');
        if (levelNode && this.dataManager?.User) {
            const levelLabel = levelNode.getComponent(Label);
            if (levelLabel) {
                levelLabel.string = this.dataManager.User.level.toString();
            }
        }
    }

    /**
     * 更新经验值显示 
     */
    updateUserExp() {
        const expNode = this.node.getChildByName('Grade')?.getChildByName('exp');
        if (expNode && this.dataManager?.User) {
            const expLabel = expNode.getComponent(Label);
            if (expLabel) {
                expLabel.string = `${this.dataManager.User.exp}/400`;
            }
        }
    }

    /**
     * 更新所有用户信息显示 
     */
    updateAllUserInfo() {
        this.updateMarbleCount();
        this.updateUserLevel();
        this.updateUserExp();
    }

    onAddClick() {
        const addNode = this.node.scene.getChildByName('Canvas').getChildByName('addmarble');
        addNode.active = true;
    }

    onDestroy() {
        // 移除事件监听
        director.off('userInited', this.userInited, this);
        director.off('updateTopBarMarbleCount', this.updateMarbleCount, this);
        director.off('updateTopBarUserLevel', this.updateUserLevel, this);
        director.off('updateTopBarUserExp', this.updateUserExp, this);
        director.off('updateTopBarAllInfo', this.updateAllUserInfo, this);
    }

    update(deltaTime: number) {

    }
}


