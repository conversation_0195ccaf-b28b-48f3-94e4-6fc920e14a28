import { _decorator, Component, instantiate, Label, Node, Prefab, resources, Sprite, SpriteFrame, UITransform } from 'cc';
import { detailFeb } from './detailFab';
import { DataManager } from '../../Managers/DataManager';
import { resultTip } from '../fab/resultTip';
const { ccclass, property } = _decorator;

/*
    展示卡片
    点击事件
*/

@ccclass('cardFeb')
export class cardFeb extends Component {

    // 详情弹窗
    @property(Prefab)
    detail: Prefab = null;

    @property(Prefab)
    resultTipPrefab: Prefab = null;

    public data: any = null;


    start() {

    }

    onClick() {
        const detailNode = instantiate(this.detail);
        this.node.scene.getChildByName('Canvas').addChild(detailNode);
        detailNode.getComponent(detailFeb).init(this.data);
        detailNode.setPosition(0, 0);
    }
    // 皮肤和特效页面调用
    init(data: any, isLocked: boolean, star: number, isCurrent: boolean) {
        this.node.getChildByName('label').active = false;
        this.node.getChildByName('tip').active = false;
        this.data = data;

        // 设置类型
        this.node.getChildByName('type').getComponent(Label).string = data.type;

        // 设置星级显示
        this.node.getChildByName('star3').active = false;
        this.node.getChildByName('star4').active = false;
        this.node.getChildByName('star5').active = false;
        if (star == 3) this.node.getChildByName('star3').active = true;
        else if (star == 4) this.node.getChildByName('star4').active = true;
        else if (star == 5) this.node.getChildByName('star5').active = true;

        // 重置状态
        this.node.getChildByName('lock').active = false;
        this.node.getChildByName('current').active = false;

        if (isLocked) {
            // 未解锁状态
            resources.load('homeUI/card0/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                this.node.getComponent(Sprite).spriteFrame = spriteFrame;
            })
            this.node.getChildByName('lock').active = true;
            if (data.unlockWay == 1) {
                this.node.getChildByName('name').getComponent(Label).string = "通过宝箱获得";
            }
            else if (data.unlockWay == 2) {
                this.node.getChildByName('name').getComponent(Label).string = "通过转盘获得";
            }
            else if (data.unlockWay == 3) {
                this.node.getChildByName('name').getComponent(Label).string = "通过活动获得";
            }
            else {
                this.node.getChildByName('name').getComponent(Label).string = "未解锁";
            }
        }
        else {
            // 已解锁状态
            const name = data.skinName || data.effectName;
            this.node.getChildByName('name').getComponent(Label).string = name;

            // 如果是当前使用的，显示当前标识
            if (isCurrent) {
                this.node.getChildByName('current').active = true;
            }
        }
        // 加载content
        if (data.skinId) {
            resources.load(`skins/${data.skinId}/spriteFrame`, SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    this.node.getChildByName('content').getComponent(Sprite).spriteFrame = spriteFrame;
                    this.node.getChildByName('content').getComponent(UITransform).setContentSize(100,100);
                }
                console.log(err);
            });
        } else if (data.launchEffectId) {
            resources.load(`spine/${data.launchEffectId}/spriteFrame`, SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    this.node.getChildByName('content').getComponent(Sprite).spriteFrame = spriteFrame;
                    this.node.getChildByName('content').getComponent(UITransform).setContentSize(100,100);
                }
            });
        } else if (data.collisionEffectId) {
            resources.load(`spine/${data.collisionEffectId}/spriteFrame`, SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    this.node.getChildByName('content').getComponent(Sprite).spriteFrame = spriteFrame;
                    this.node.getChildByName('content').getComponent(UITransform).setContentSize(100,100);
                }
            });
        } else if (data.trailEffectId) {
            resources.load(`spine/${data.trailEffectId}/spriteFrame`, SpriteFrame, (err, spriteFrame) => {
                if (!err && spriteFrame) {
                    this.node.getChildByName('content').getComponent(Sprite).spriteFrame = spriteFrame;
                    this.node.getChildByName('content').getComponent(UITransform).setContentSize(100,100);
                }
            });
        }
        
        this.node.on('click', this.onClick, this);
    }

    // 商城界面调用
    init1(data: any, isUnlocked: boolean) {
        this.node.getChildByName('name').active = false;

        this.data = data;
        this.node.getChildByName('type').getComponent(Label).string = data.type;
        if (isUnlocked) {
            resources.load('homeUI/免费/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                this.node.getChildByName('tip').getChildByName('status').getComponent(Sprite).spriteFrame = spriteFrame;
            })
            resources.load('homeUI/card1/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                this.node.getComponent(Sprite).spriteFrame = spriteFrame;
            })
            resources.load('homeUI/黄1/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                this.node.getChildByName('label').getComponent(Sprite).spriteFrame = spriteFrame;
            })
            this.node.on('click', this.onWatchvideo, this);
        }
        else {
            resources.load('homeUI/已售罄/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                this.node.getChildByName('tip').getChildByName('status').getComponent(Sprite).spriteFrame = spriteFrame;
            })
            resources.load('homeUI/card0/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                this.node.getComponent(Sprite).spriteFrame = spriteFrame;
            })
            resources.load('homeUI/黄0/spriteFrame', SpriteFrame, (err, spriteFrame) => {
                this.node.getChildByName('label').getComponent(Sprite).spriteFrame = spriteFrame;
            })
        }

    }
    onWatchvideo() {
        // 观看视频后，给玩家对应奖励data
        const dataManager = DataManager.getInstance();
        const user = dataManager.User;

        if (!user || !this.data) return;

        // 判断是皮肤还是特效
        const isSkin = this.data.skinName !== undefined;

        // 给予1天体验时间
        if (isSkin) {
            const skinId = this.data.skinId;
            user.addTimedSkin(skinId, 1);
            console.log(`获得皮肤体验：${this.data.skinName}，有效期1天`);
            this.showResultTip(`[限时]${this.data.skinName}`, 1);
        } else if (this.data.launchEffectId) {
            const launchEffectId = this.data.launchEffectId;
            user.addTimedLaunchEffect(launchEffectId, 1);
            console.log(`获得发射特效体验：${this.data.effectName}，有效期1天`);
            this.showResultTip(`[限时]${this.data.effectName}`, 1);
        } else if (this.data.collisionEffectId) {
            const collisionEffectId = this.data.collisionEffectId;
            user.addTimedCollisionEffect(collisionEffectId, 1);
            console.log(`获得碰撞特效体验：${this.data.effectName}，有效期1天`);
            this.showResultTip(`[限时]${this.data.effectName}`, 1);
        } else if (this.data.trailEffectId) {
            const trailEffectId = this.data.trailEffectId;
            user.addTimedTrailEffect(trailEffectId, 1);
            console.log(`获得拖尾特效体验：${this.data.effectName}，有效期1天`);
            this.showResultTip(`[限时]${this.data.effectName}`, 1);
        }

        // 刷新UI状态
        this.refreshUI();
    }

    // 刷新UI状态
    refreshUI() {
        const dataManager = DataManager.getInstance();
        const user = dataManager.User;
        if (!user || !this.data) return;

        const isSkin = this.data.skinName !== undefined;

        let isUnlocked = false;
        if (isSkin) {
            const skinId = this.data.skinId;
            isUnlocked = user.hasSkin(skinId);
        } else if (this.data.launchEffectId) {
            const launchEffectId = this.data.launchEffectId;
            isUnlocked = user.hasLaunchEffect(launchEffectId);
        } else if (this.data.collisionEffectId) {
            const collisionEffectId = this.data.collisionEffectId;
            isUnlocked = user.hasCollisionEffect(collisionEffectId);
        } else if (this.data.trailEffectId) {
            const trailEffectId = this.data.trailEffectId;
            isUnlocked = user.hasTrailEffect(trailEffectId);
        }

        // 重新设置UI状态
        this.init1(this.data, isUnlocked);
    }

    update(deltaTime: number) {

    }

    // 显示恭喜获得弹窗
    showResultTip(itemName: string, count: number) {
        if (!this.resultTipPrefab) return;

        const resultTipNode = instantiate(this.resultTipPrefab);
        const resultTipComponent = resultTipNode.getComponent(resultTip);
        if (resultTipComponent) {
            resultTipComponent.set([{ name: itemName, count: count }]);
        }

        // 添加到场景根节点
        this.node.scene.getChildByName('Canvas').addChild(resultTipNode);
    }
}


